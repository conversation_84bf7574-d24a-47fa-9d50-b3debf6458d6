# FastAPI Auth 系统配置文件示例
# 复制此文件为 config.yml 并根据需要修改配置

# 应用基本设置
app_name: "FastAPI Auth System"
host: "0.0.0.0"
app_port: 8000
reload: true

# 数据库配置
database_url: "postgresql+asyncpg://username:password@localhost:5432/fastapi_auth"

# JWT 配置
secret_key: "your-super-secret-key-change-this-in-production"
algorithm: "HS256"
access_token_expire_time: 30  # 分钟
refresh_token_expire_time: 10080  # 分钟 (7天)

# Redis 配置
redis_config:
  host: "127.0.0.1"
  port: 16379
  db: 0
  password: null
  decode_responses: true
  max_connections: 20
  socket_connect_timeout: 5
  socket_timeout: 5
  retry_on_timeout: true
  health_check_interval: 30

# CORS 配置
cors_origins:
  - "http://localhost:3000"
  - "http://localhost:8080"
cors_credentials: true

# 启动时初始化设置
init_admin_on_startup: true  # 设置为 false 可以禁用启动时自动创建管理员用户

# 用户代理列表
user_agents:
  - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

# HTTP 设置
http_timeout: 30.0
download_timeout: 30.0

# NAS 设置
nas_base_path: "/app/videos"

# Notion 集成（可选）
notion_api_key: ""
notion_database_id: ""
push_to_notion: false

# API 权限配置文件路径
api_permissions_config: "config/api_permissions.yml"

# app/db/init_db.py

"""
数据库初始化模块

负责创建数据库目录、初始化数据库表结构等功能。
确保应用程序启动时数据库环境正确配置。
"""

import os
from app.db.database import async_engine
from app.models.base_models import Base
from app.config.env import settings
from loguru import logger


async def init_db():
    """初始化数据库，创建所有表"""
    # 确保数据目录存在
    data_dir = settings.ROOT_DIR / "data"
    os.makedirs(data_dir, exist_ok=True)

    logger.info(f"创建数据库目录: {data_dir}")
    logger.info(f"使用数据库URL: {settings.DATABASE_URL}")

    # 创建所有表
    try:
        async with async_engine.begin() as conn:
            logger.info("创建数据库表...")
            await conn.run_sync(Base.metadata.create_all)
        logger.success("数据库表创建成功")
        

    except Exception as e:
        logger.error(f"创建数据库表时出错: {e}")
        raise

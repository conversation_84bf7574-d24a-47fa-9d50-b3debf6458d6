#!/usr/bin/env python3
"""
初始化管理员用户脚本

这个脚本会：
1. 初始化所有角色
2. 初始化所有权限
3. 为管理员角色分配所有权限
4. 创建默认管理员用户 (用户名: admin, 密码: admin)

使用方法:
python scripts/init_admin.py
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from app.db.session import get_async_session
from app.services.init_data_service import init_admin


async def main():
    """主函数"""
    try:
        logger.info("Starting admin user initialization...")

        # 使用标准异步会话
        async for db in get_async_session():
            try:
                # 初始化管理员用户和基础数据
                result = await init_admin(db)

                if result["success"]:
                    await db.commit()  # 手动提交事务
                    logger.success("✅ Admin user initialization completed successfully!")
                    logger.info(f"📋 Result: {result['message']}")

                    if result.get('created'):
                        logger.warning("🔐 Default admin credentials:")
                        logger.warning("   Username: admin")
                        logger.warning("   Password: admin")
                        logger.warning("   ⚠️  Please change the default password after first login!")
                    else:
                        logger.info("Admin user already exists")

                else:
                    logger.error("❌ Admin user initialization failed")
                    await db.rollback()
                    raise Exception("Admin user initialization failed")

            except Exception as e:
                logger.error(f"❌ Error during initialization: {e}")
                await db.rollback()
                raise
            break  # 只使用一个数据库会话

    except Exception as e:
        logger.error(f"❌ Failed to initialize admin user: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 运行主函数
    asyncio.run(main())

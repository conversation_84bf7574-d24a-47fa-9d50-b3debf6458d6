#!/usr/bin/env python3
"""
执行 ApiPermission → ApiScopes 迁移

这个脚本会：
1. 检查当前数据库状态
2. 执行 Alembic 迁移
3. 验证迁移结果
4. 提供回滚选项
"""

import asyncio
import sys
import os
import subprocess

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from sqlalchemy import text
from app.db.session import get_async_transaction_session


async def check_database_status():
    """检查数据库当前状态"""
    logger.info("=== 检查数据库状态 ===")
    
    async with get_async_transaction_session() as db:
        try:
            # 检查表是否存在
            tables_to_check = [
                'api_permissions',
                'api_scopes', 
                'associate_token_api_permissions',
                'associate_token_api_scopes'
            ]
            
            table_status = {}
            for table in tables_to_check:
                result = await db.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'auth' 
                        AND table_name = :table_name
                    )
                """), {"table_name": table})
                table_status[table] = result.scalar()
            
            logger.info("📊 数据库表状态:")
            for table, exists in table_status.items():
                status = "✅ 存在" if exists else "❌ 不存在"
                logger.info(f"   {table}: {status}")
            
            # 检查数据量
            if table_status['api_permissions']:
                result = await db.execute(text("SELECT COUNT(*) FROM auth.api_permissions"))
                count = result.scalar()
                logger.info(f"   api_permissions 记录数: {count}")
            
            if table_status['api_scopes']:
                result = await db.execute(text("SELECT COUNT(*) FROM auth.api_scopes"))
                count = result.scalar()
                logger.info(f"   api_scopes 记录数: {count}")
            
            return table_status
            
        except Exception as e:
            logger.error(f"❌ 检查数据库状态失败: {e}")
            return None


async def check_alembic_status():
    """检查 Alembic 迁移状态"""
    logger.info("=== 检查 Alembic 状态 ===")
    
    try:
        # 检查当前版本
        result = subprocess.run(['alembic', 'current'], 
                              capture_output=True, text=True, cwd=os.path.dirname(os.path.dirname(__file__)))
        
        if result.returncode == 0:
            current_version = result.stdout.strip()
            logger.info(f"当前 Alembic 版本: {current_version}")
        else:
            logger.error(f"获取 Alembic 版本失败: {result.stderr}")
            return False
        
        # 检查待执行的迁移
        result = subprocess.run(['alembic', 'heads'], 
                              capture_output=True, text=True, cwd=os.path.dirname(os.path.dirname(__file__)))
        
        if result.returncode == 0:
            heads = result.stdout.strip()
            logger.info(f"最新 Alembic 版本: {heads}")
        else:
            logger.error(f"获取 Alembic heads 失败: {result.stderr}")
            return False
        
        return True
        
    except FileNotFoundError:
        logger.error("❌ 找不到 alembic 命令，请确保已安装 alembic")
        return False
    except Exception as e:
        logger.error(f"❌ 检查 Alembic 状态失败: {e}")
        return False


def run_alembic_command(command: list[str]) -> bool:
    """执行 Alembic 命令"""
    try:
        logger.info(f"执行命令: alembic {' '.join(command)}")
        
        result = subprocess.run(['alembic'] + command, 
                              capture_output=True, text=True, 
                              cwd=os.path.dirname(os.path.dirname(__file__)))
        
        if result.returncode == 0:
            logger.success(f"✅ 命令执行成功")
            if result.stdout.strip():
                logger.info(f"输出: {result.stdout.strip()}")
            return True
        else:
            logger.error(f"❌ 命令执行失败")
            logger.error(f"错误: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 执行 Alembic 命令失败: {e}")
        return False


async def verify_migration():
    """验证迁移结果"""
    logger.info("=== 验证迁移结果 ===")
    
    async with get_async_transaction_session() as db:
        try:
            # 检查新表是否存在
            result = await db.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'auth' 
                    AND table_name = 'api_scopes'
                )
            """))
            api_scopes_exists = result.scalar()
            
            if not api_scopes_exists:
                logger.error("❌ api_scopes 表不存在")
                return False
            
            logger.success("✅ api_scopes 表存在")
            
            # 检查数据
            result = await db.execute(text("SELECT COUNT(*) FROM auth.api_scopes"))
            count = result.scalar()
            logger.info(f"api_scopes 表记录数: {count}")
            
            # 检查表结构
            result = await db.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_schema = 'auth' 
                AND table_name = 'api_scopes'
                ORDER BY ordinal_position
            """))
            columns = result.fetchall()
            
            logger.info("📋 表结构:")
            for column_name, data_type in columns:
                logger.info(f"   {column_name}: {data_type}")
            
            # 检查关联表
            result = await db.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'auth' 
                    AND table_name = 'associate_token_api_scopes'
                )
            """))
            assoc_exists = result.scalar()
            
            if assoc_exists:
                logger.success("✅ associate_token_api_scopes 表存在")
                
                # 检查关联表列
                result = await db.execute(text("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_schema = 'auth' 
                    AND table_name = 'associate_token_api_scopes'
                    ORDER BY column_name
                """))
                assoc_columns = [row[0] for row in result.fetchall()]
                logger.info(f"关联表列: {assoc_columns}")
                
                expected_columns = ['api_scope_id', 'token_id']
                if all(col in assoc_columns for col in expected_columns):
                    logger.success("✅ 关联表结构正确")
                else:
                    logger.error(f"❌ 关联表结构错误，期望: {expected_columns}")
                    return False
            else:
                logger.info("ℹ️ associate_token_api_scopes 表不存在（可能没有数据）")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 验证迁移失败: {e}")
            return False


async def main():
    """主函数"""
    try:
        logger.info("开始 ApiPermission → ApiScopes 迁移...")
        
        # 检查命令行参数
        if len(sys.argv) > 1:
            if sys.argv[1] == '--rollback':
                # 回滚模式
                logger.warning("⚠️ 回滚模式")
                confirm = input("确定要回滚迁移吗？这将把 api_scopes 改回 api_permissions (y/N): ")
                if confirm.lower() != 'y':
                    logger.info("取消回滚")
                    return
                
                success = run_alembic_command(['downgrade', '-1'])
                if success:
                    logger.success("🔄 回滚完成")
                else:
                    logger.error("❌ 回滚失败")
                    sys.exit(1)
                return
                
            elif sys.argv[1] == '--status':
                # 仅检查状态
                await check_database_status()
                await check_alembic_status()
                return
        
        # 1. 检查数据库状态
        table_status = await check_database_status()
        if table_status is None:
            sys.exit(1)
        
        # 2. 检查 Alembic 状态
        alembic_ok = await check_alembic_status()
        if not alembic_ok:
            sys.exit(1)
        
        # 3. 确认迁移
        if table_status.get('api_scopes', False):
            logger.info("ℹ️ api_scopes 表已存在，可能已经迁移过了")
            verify_result = await verify_migration()
            if verify_result:
                logger.success("🎉 迁移验证通过，无需重复迁移")
            return
        
        if not table_status.get('api_permissions', False):
            logger.warning("⚠️ api_permissions 表不存在，将创建新的 api_scopes 表")
        
        confirm = input("确定要执行迁移吗？(y/N): ")
        if confirm.lower() != 'y':
            logger.info("取消迁移")
            return
        
        # 4. 执行迁移
        logger.info("🚀 开始执行迁移...")
        success = run_alembic_command(['upgrade', 'head'])
        
        if not success:
            logger.error("❌ 迁移失败")
            sys.exit(1)
        
        # 5. 验证迁移结果
        verify_result = await verify_migration()
        
        if verify_result:
            logger.success("🎉 迁移成功完成！")
            logger.info("\n📋 迁移总结:")
            logger.info("   ✅ api_permissions → api_scopes")
            logger.info("   ✅ associate_token_api_permissions → associate_token_api_scopes")
            logger.info("   ✅ api_permission_id → api_scope_id")
            logger.info("\n💡 提示:")
            logger.info("   - 代码中已添加向后兼容别名")
            logger.info("   - 如需回滚: python scripts/run_migration.py --rollback")
            logger.info("   - 检查状态: python scripts/run_migration.py --status")
        else:
            logger.error("❌ 迁移验证失败")
            logger.warning("建议检查数据库状态或考虑回滚")
            sys.exit(1)
        
    except KeyboardInterrupt:
        logger.warning("⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 迁移过程失败: {e}")
        import traceback
        logger.error(f"完整错误信息:\n{traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level="INFO"
    )
    
    # 显示帮助信息
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        print("ApiPermission → ApiScopes 迁移工具")
        print("")
        print("用法:")
        print("  python scripts/run_migration.py           # 执行迁移")
        print("  python scripts/run_migration.py --status  # 检查状态")
        print("  python scripts/run_migration.py --rollback # 回滚迁移")
        print("  python scripts/run_migration.py --help    # 显示帮助")
        sys.exit(0)
    
    # 运行迁移
    asyncio.run(main())

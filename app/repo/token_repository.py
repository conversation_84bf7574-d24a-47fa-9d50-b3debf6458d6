

from datetime import datetime, timezone
import redis.asyncio as redis
from typing import Optional
from loguru import logger
from fastapi import Request



class RefreshTokenRepository:
    """Refresh Token Redis 仓储类"""

    def __init__(self, request: Request):
        """
        初始化仓储，从应用状态获取Redis客户端

        Args:
            request: FastAPI请求对象
        """
        self.redis_client: redis.Redis = request.app.state.redis  # type: ignore
        if not self.redis_client:
            logger.warning("Redis client not available, refresh token functionality will be disabled")



    async def store_refresh_token(self, username: str, payload: dict) -> bool:
        """
        在 Redis 中存储 refresh token 及相关信息

        Args:
            username: 用户名
            payload: JWT payload

        Returns:
            bool: 存储是否成功
        """
        if not self.redis_client:
            logger.warning("Redis client not available, cannot store refresh token")
            return False

        try:

            sub = payload.get("sub")
            if not sub:
                raise ValueError(
                    f"Failed to store refresh token for {username}: sub is None"
                )

            exp = payload.get("exp")
            expire_seconds = exp - datetime.now(timezone.utc).timestamp()

            # 检查token是否已经过期
            if expire_seconds <= 0:
                logger.warning("Token already expired")
                return False

            # 使用jti作为key（如果有的话），否则使用username
            key = f"refresh_token:{payload.get('jti', username)}"

            # 存储到Redis
            result = await self.redis_client.setex(
                key,
                int(expire_seconds),
                sub
            )

            logger.info(
                f"Refresh token stored - key: {key}, sub: {sub}, expires in {expire_seconds} seconds")
            return bool(result)

        except Exception as e:
            logger.error(f"Failed to store refresh token for {username}: {e}")
            return False

    async def get_stored_refresh_token(self, jti: str) -> Optional[str]:
        """
        从 Redis 中获取存储的用户名

        Args:
            jti: refresh token 的 JTI

        Returns:
            Optional[str]: 存储的用户名 或 None
        """
        if not self.redis_client:
            logger.warning("Redis client not available, cannot get refresh token")
            return None

        try:
            key = f"refresh_token:{jti}"
            stored_value = await self.redis_client.get(key)


            if stored_value:
                # Redis 返回的是 bytes，需要解码为字符串
                username = stored_value.decode() if isinstance(stored_value, bytes) else stored_value
                logger.debug(f"Refresh token found for user: {username}")
                return username
            return None
        except Exception as e:
            # 特别处理连接错误
            if "connection" in str(e).lower() or "10054" in str(e):
                logger.error(f"Redis connection error for JTI {jti}: {e}")
            else:
                logger.error(f"Failed to get refresh token for {jti}: {e}")
            return None


    async def revoke_refresh_token(self, jti: str) -> bool:
        """
        撤销 refresh token

        Args:
            jti: refresh token 的 JTI

        Returns:
            bool: 撤销是否成功
        """
        if not self.redis_client:
            logger.warning("Redis client not available, cannot revoke refresh token")
            return False

        try:
            key = f"refresh_token:{jti}"
            # 先获取存储的用户名用于日志
            stored_username = await self.redis_client.get(key)
            result = await self.redis_client.delete(key)
            if stored_username:
                logger.info(f"Refresh token revoked for user: {stored_username.decode() if isinstance(stored_username, bytes) else stored_username}")
            return bool(result)
        except Exception as e:
            logger.error(f"Failed to revoke refresh token for {jti}: {e}")
            return False

    async def is_refresh_token_valid(self, jti: str, token: str) -> bool:
        """
        验证 refresh token 是否有效

        Args:
            jti: refresh token 的 JTI
            token: 要验证的token

        Returns:
            bool: token是否有效
        """
        try:
            stored_token = await self.get_stored_refresh_token(jti)
            return stored_token == token if stored_token else False
        except Exception as e:
            logger.error(f"Failed to validate refresh token for {jti}: {e}")
            return False

    async def list_all_tokens(self) -> list[dict]:
        """
        列出所有活跃的 refresh tokens（仅用于开发环境）

        Returns:
            list[dict]: 包含所有 token 信息的列表
        """

        #todo 有没有更好的办法去管理redis_client的连接？

        if not self.redis_client:
            logger.warning("Redis client not available, cannot list tokens")
            return []

        try:
            # 获取所有 refresh_token:* 的 keys
            pattern = "refresh_token:*"
            keys = await self.redis_client.keys(pattern)

            tokens = []
            for key in keys:
                try:
                    username = await self.redis_client.get(key)
                    ttl = await self.redis_client.ttl(key)

                    if username:
                        jti = key.decode() if isinstance(key, bytes) else key
                        jti = jti.replace("refresh_token:", "")

                        tokens.append({
                            "jti": jti,
                            "username": username.decode() if isinstance(username, bytes) else username,
                            "ttl_seconds": ttl,
                            "expires_in_hours": round(ttl / 3600, 2) if ttl > 0 else 0
                        })
                except Exception as e:
                    logger.error(f"Error processing token key {key}: {e}")
                    continue

            logger.info(f"Found {len(tokens)} active refresh tokens")
            return tokens

        except Exception as e:
            logger.error(f"Failed to list refresh tokens: {e}")
            return []

    async def revoke_all_user_tokens(self, username: str) -> int:
        """
        撤销指定用户的所有 refresh tokens

        Args:
            username: 用户名

        Returns:
            int: 撤销的 token 数量
        """
        # todo 优化连接redis
        if not self.redis_client:
            logger.warning("Redis client not available, cannot revoke tokens")
            return 0

        try:
            # 获取所有 refresh_token:* 的 keys
            pattern = "refresh_token:*"
            keys = await self.redis_client.keys(pattern)

            revoked_count = 0
            for key in keys:
                try:
                    stored_username = await self.redis_client.get(key)
                    if stored_username:
                        stored_username = stored_username.decode() if isinstance(stored_username, bytes) else stored_username
                        if stored_username == username:
                            await self.redis_client.delete(key)
                            revoked_count += 1
                except Exception as e:
                    logger.error(f"Error processing token key {key}: {e}")
                    continue

            logger.info(f"Revoked {revoked_count} refresh tokens for user: {username}")
            return revoked_count

        except Exception as e:
            logger.error(f"Failed to revoke all tokens for user {username}: {e}")
            return 0


# 获取 RefreshTokenRepository 实例的依赖函数
def get_refresh_token_repository(request: Request) -> RefreshTokenRepository:
    """
    获取 RefreshTokenRepository 实例的依赖函数
    """
    redis_client = request.app.state.redis  # type: ignore
    if not redis_client:
        raise RuntimeError("Redis client not initialized")
    return RefreshTokenRepository(request)
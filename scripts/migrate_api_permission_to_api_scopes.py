#!/usr/bin/env python3
"""
数据库迁移脚本：ApiPermission → ApiScopes

将 api_permissions 表重命名为 api_scopes，并更新相关的关联表
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from sqlalchemy import text
from app.db.session import get_async_transaction_session


async def check_table_exists(db, table_name: str, schema: str = 'auth') -> bool:
    """检查表是否存在"""
    result = await db.execute(text("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = :schema 
            AND table_name = :table_name
        )
    """), {"schema": schema, "table_name": table_name})
    
    return result.scalar()


async def migrate_tables(db):
    """执行表迁移"""
    logger.info("开始数据库表迁移...")
    
    # 1. 检查原表是否存在
    api_permissions_exists = await check_table_exists(db, 'api_permissions')
    api_scopes_exists = await check_table_exists(db, 'api_scopes')
    
    logger.info(f"api_permissions 表存在: {api_permissions_exists}")
    logger.info(f"api_scopes 表存在: {api_scopes_exists}")
    
    if not api_permissions_exists and not api_scopes_exists:
        logger.warning("两个表都不存在，跳过迁移")
        return
    
    if api_scopes_exists:
        logger.info("api_scopes 表已存在，跳过迁移")
        return
    
    if api_permissions_exists:
        logger.info("开始迁移 api_permissions → api_scopes...")
        
        # 2. 重命名主表
        await db.execute(text("""
            ALTER TABLE auth.api_permissions 
            RENAME TO api_scopes
        """))
        logger.success("✅ 主表重命名完成")
        
        # 3. 检查并迁移关联表
        assoc_old_exists = await check_table_exists(db, 'associate_token_api_permissions')
        assoc_new_exists = await check_table_exists(db, 'associate_token_api_scopes')
        
        if assoc_old_exists and not assoc_new_exists:
            logger.info("迁移关联表...")
            
            # 重命名关联表
            await db.execute(text("""
                ALTER TABLE auth.associate_token_api_permissions 
                RENAME TO associate_token_api_scopes
            """))
            
            # 重命名外键列
            await db.execute(text("""
                ALTER TABLE auth.associate_token_api_scopes 
                RENAME COLUMN api_permission_id TO api_scope_id
            """))
            
            logger.success("✅ 关联表迁移完成")
        
        # 4. 更新外键约束名称（如果需要）
        try:
            # 这里可以添加更新约束名称的逻辑
            logger.info("外键约束更新完成")
        except Exception as e:
            logger.warning(f"外键约束更新失败（可能不需要）: {e}")
        
        logger.success("🎉 数据库迁移完成！")


async def verify_migration(db):
    """验证迁移结果"""
    logger.info("验证迁移结果...")
    
    # 检查新表是否存在
    api_scopes_exists = await check_table_exists(db, 'api_scopes')
    assoc_exists = await check_table_exists(db, 'associate_token_api_scopes')
    
    if api_scopes_exists:
        logger.success("✅ api_scopes 表存在")
        
        # 检查数据
        result = await db.execute(text("SELECT COUNT(*) FROM auth.api_scopes"))
        count = result.scalar()
        logger.info(f"api_scopes 表记录数: {count}")
    else:
        logger.error("❌ api_scopes 表不存在")
        return False
    
    if assoc_exists:
        logger.success("✅ associate_token_api_scopes 表存在")
        
        # 检查列是否正确
        result = await db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = 'auth' 
            AND table_name = 'associate_token_api_scopes'
            ORDER BY column_name
        """))
        columns = [row[0] for row in result.fetchall()]
        logger.info(f"关联表列: {columns}")
        
        expected_columns = ['api_scope_id', 'token_id']
        if all(col in columns for col in expected_columns):
            logger.success("✅ 关联表列名正确")
        else:
            logger.error(f"❌ 关联表列名错误，期望: {expected_columns}, 实际: {columns}")
            return False
    else:
        logger.warning("⚠️ associate_token_api_scopes 表不存在（可能没有数据）")
    
    return True


async def rollback_migration(db):
    """回滚迁移（如果需要）"""
    logger.warning("开始回滚迁移...")
    
    try:
        # 检查新表是否存在
        api_scopes_exists = await check_table_exists(db, 'api_scopes')
        
        if api_scopes_exists:
            # 回滚主表
            await db.execute(text("""
                ALTER TABLE auth.api_scopes 
                RENAME TO api_permissions
            """))
            logger.info("主表回滚完成")
            
            # 回滚关联表
            assoc_exists = await check_table_exists(db, 'associate_token_api_scopes')
            if assoc_exists:
                await db.execute(text("""
                    ALTER TABLE auth.associate_token_api_scopes 
                    RENAME COLUMN api_scope_id TO api_permission_id
                """))
                
                await db.execute(text("""
                    ALTER TABLE auth.associate_token_api_scopes 
                    RENAME TO associate_token_api_permissions
                """))
                logger.info("关联表回滚完成")
        
        logger.success("🔄 迁移回滚完成")
        
    except Exception as e:
        logger.error(f"❌ 回滚失败: {e}")
        raise


async def main():
    """主函数"""
    try:
        logger.info("开始 ApiPermission → ApiScopes 迁移...")
        
        # 检查命令行参数
        if len(sys.argv) > 1 and sys.argv[1] == '--rollback':
            # 回滚模式
            async with get_async_transaction_session() as db:
                await rollback_migration(db)
                await db.commit()
            return
        
        # 正常迁移模式
        async with get_async_transaction_session() as db:
            # 执行迁移
            await migrate_tables(db)
            
            # 验证迁移
            success = await verify_migration(db)
            
            if success:
                await db.commit()
                logger.success("🎉 迁移成功完成并已提交！")
                
                logger.info("\n📋 迁移总结:")
                logger.info("   - api_permissions → api_scopes")
                logger.info("   - associate_token_api_permissions → associate_token_api_scopes")
                logger.info("   - api_permission_id → api_scope_id")
                logger.info("\n⚠️  注意事项:")
                logger.info("   - 代码中已添加向后兼容别名")
                logger.info("   - 如需回滚，请运行: python scripts/migrate_api_permission_to_api_scopes.py --rollback")
            else:
                await db.rollback()
                logger.error("❌ 迁移验证失败，已回滚")
                sys.exit(1)
        
    except Exception as e:
        logger.error(f"❌ 迁移失败: {e}")
        import traceback
        logger.error(f"完整错误信息:\n{traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level="INFO"
    )
    
    # 运行迁移
    asyncio.run(main())

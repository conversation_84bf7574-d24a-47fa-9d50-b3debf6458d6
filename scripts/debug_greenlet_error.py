#!/usr/bin/env python3
"""
调试 greenlet 错误的脚本

这个脚本会逐步测试数据库连接和会话，帮助定位 greenlet 错误的原因
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from sqlalchemy import text
from app.db.session import get_async_transaction_session, get_async_session
from app.db.database import async_engine, AsyncSessionLocal
from app.repo.user_repository import UserRepository, RoleRepository
from app.config.enums import RoleType


async def test_basic_connection():
    """测试基本数据库连接"""
    logger.info("=== 测试基本数据库连接 ===")
    
    try:
        async with async_engine.begin() as conn:
            result = await conn.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            logger.success(f"✅ 基本连接测试成功: {row}")
            return True
    except Exception as e:
        logger.error(f"❌ 基本连接测试失败: {e}")
        return False


async def test_session_creation():
    """测试会话创建"""
    logger.info("=== 测试会话创建 ===")
    
    try:
        async with AsyncSessionLocal() as session:
            logger.success("✅ 会话创建成功")
            return True
    except Exception as e:
        logger.error(f"❌ 会话创建失败: {e}")
        return False


async def test_get_async_session():
    """测试 get_async_session 函数"""
    logger.info("=== 测试 get_async_session ===")
    
    try:
        async for db in get_async_session():
            logger.success("✅ get_async_session 测试成功")
            break
        return True
    except Exception as e:
        logger.error(f"❌ get_async_session 测试失败: {e}")
        return False


async def test_get_async_transaction_session():
    """测试 get_async_transaction_session 函数"""
    logger.info("=== 测试 get_async_transaction_session ===")
    
    try:
        async with get_async_transaction_session() as db:
            logger.success("✅ get_async_transaction_session 测试成功")
            return True
    except Exception as e:
        logger.error(f"❌ get_async_transaction_session 测试失败: {e}")
        return False


async def test_simple_query():
    """测试简单查询"""
    logger.info("=== 测试简单查询 ===")
    
    try:
        async with get_async_transaction_session() as db:
            result = await db.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            logger.success(f"✅ 简单查询测试成功: {row}")
            return True
    except Exception as e:
        logger.error(f"❌ 简单查询测试失败: {e}")
        return False


async def test_repository_creation():
    """测试仓储类创建"""
    logger.info("=== 测试仓储类创建 ===")
    
    try:
        async with get_async_transaction_session() as db:
            user_repo = UserRepository(db)
            role_repo = RoleRepository(db)
            logger.success("✅ 仓储类创建成功")
            return True
    except Exception as e:
        logger.error(f"❌ 仓储类创建失败: {e}")
        return False


async def test_role_query():
    """测试角色查询"""
    logger.info("=== 测试角色查询 ===")
    
    try:
        async with get_async_transaction_session() as db:
            role_repo = RoleRepository(db)
            
            # 尝试查询所有角色
            roles = await role_repo.get_all()
            logger.info(f"查询到 {len(roles)} 个角色")
            
            # 尝试按名称查询角色
            admin_role = await role_repo.get_one_by_name(RoleType.ADMIN.value)
            logger.info(f"ADMIN 角色: {admin_role}")
            
            logger.success("✅ 角色查询测试成功")
            return True
    except Exception as e:
        logger.error(f"❌ 角色查询测试失败: {e}")
        return False


async def test_user_query():
    """测试用户查询"""
    logger.info("=== 测试用户查询 ===")
    
    try:
        async with get_async_transaction_session() as db:
            user_repo = UserRepository(db)
            
            # 尝试查询管理员用户
            admin_user = await user_repo.get_one_by_username("admin")
            logger.info(f"Admin 用户: {admin_user}")
            
            logger.success("✅ 用户查询测试成功")
            return True
    except Exception as e:
        logger.error(f"❌ 用户查询测试失败: {e}")
        return False


async def main():
    """主函数"""
    logger.info("开始 greenlet 错误诊断...")
    
    tests = [
        ("基本连接", test_basic_connection),
        ("会话创建", test_session_creation),
        ("get_async_session", test_get_async_session),
        ("get_async_transaction_session", test_get_async_transaction_session),
        ("简单查询", test_simple_query),
        ("仓储类创建", test_repository_creation),
        ("角色查询", test_role_query),
        ("用户查询", test_user_query),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            logger.error(f"测试 '{test_name}' 异常: {e}")
            results[test_name] = False
            # 如果某个测试失败，我们继续下一个测试
            continue
    
    # 总结结果
    logger.info("\n=== 测试结果总结 ===")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    # 找出第一个失败的测试
    failed_tests = [name for name, result in results.items() if not result]
    if failed_tests:
        logger.error(f"\n第一个失败的测试: {failed_tests[0]}")
        logger.error("这可能是 greenlet 错误的根源")
    else:
        logger.success("\n🎉 所有测试都通过了！")


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level="INFO"
    )
    
    # 运行诊断
    asyncio.run(main())

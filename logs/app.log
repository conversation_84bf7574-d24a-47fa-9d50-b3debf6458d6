2025-07-10 17:05:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:05:06 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:05:06 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:05:06 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPIProject\data
2025-07-10 17:05:06 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPIProject/data/db.sqlite3
2025-07-10 17:05:06 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-10 17:05:06 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-10 17:05:06 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-10 17:07:05 | SUCCESS  | app.core.init_data:init_roles:46 - 角色初始化完成 - 角色 创建: 0, 删除: 0；
2025-07-10 17:07:05 | SUCCESS  | app.core.init_data:init_permissions:89 - 权限初始化完成 - 权限 创建: 0, 删除: 0
2025-07-10 17:07:05 | SUCCESS  | app.api.v1.auth.permissions:init_roles_permissions:50 - 基本角色、权限初始化成功
2025-07-10 17:07:07 | SUCCESS  | app.core.init_data:init_roles:46 - 角色初始化完成 - 角色 创建: 0, 删除: 0；
2025-07-10 17:07:07 | SUCCESS  | app.core.init_data:init_permissions:89 - 权限初始化完成 - 权限 创建: 0, 删除: 0
2025-07-10 17:07:07 | SUCCESS  | app.api.v1.auth.permissions:init_roles_permissions:50 - 基本角色、权限初始化成功
2025-07-10 17:10:03 | INFO     | app.main:lifespan:30 - 正在关闭数据库引擎...
2025-07-10 17:10:03 | INFO     | app.main:lifespan:32 - 数据库引擎已关闭
2025-07-10 17:10:03 | INFO     | app.main:lifespan:42 - 正在关闭抖音分析浏览器...
2025-07-10 17:10:03 | INFO     | app.main:lifespan:44 - 抖音分析浏览器已关闭
2025-07-10 17:10:03 | INFO     | app.main:lifespan:48 - 抖音推送下载app_env关闭成功
2025-07-10 17:10:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:10:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:10:05 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPIProject\data
2025-07-10 17:10:05 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPIProject/data/db.sqlite3
2025-07-10 17:10:05 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-10 17:10:05 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-10 17:10:05 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-10 17:15:04 | INFO     | app.main:lifespan:30 - 正在关闭数据库引擎...
2025-07-10 17:15:04 | INFO     | app.main:lifespan:32 - 数据库引擎已关闭
2025-07-10 17:15:04 | INFO     | app.main:lifespan:42 - 正在关闭抖音分析浏览器...
2025-07-10 17:15:04 | INFO     | app.main:lifespan:44 - 抖音分析浏览器已关闭
2025-07-10 17:15:04 | INFO     | app.main:lifespan:48 - 抖音推送下载app_env关闭成功
2025-07-10 17:15:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:15:06 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:15:06 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPIProject\data
2025-07-10 17:15:06 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPIProject/data/db.sqlite3
2025-07-10 17:15:06 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-10 17:15:06 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-10 17:15:06 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-10 17:20:04 | INFO     | app.main:lifespan:30 - 正在关闭数据库引擎...
2025-07-10 17:20:04 | INFO     | app.main:lifespan:32 - 数据库引擎已关闭
2025-07-10 17:20:04 | INFO     | app.main:lifespan:42 - 正在关闭抖音分析浏览器...
2025-07-10 17:20:04 | INFO     | app.main:lifespan:44 - 抖音分析浏览器已关闭
2025-07-10 17:20:04 | INFO     | app.main:lifespan:48 - 抖音推送下载app_env关闭成功
2025-07-10 17:20:06 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:20:06 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:20:06 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPIProject\data
2025-07-10 17:20:06 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPIProject/data/db.sqlite3
2025-07-10 17:20:06 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-10 17:20:06 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-10 17:20:06 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-10 17:30:04 | INFO     | app.main:lifespan:30 - 正在关闭数据库引擎...
2025-07-10 17:30:04 | INFO     | app.main:lifespan:32 - 数据库引擎已关闭
2025-07-10 17:30:04 | INFO     | app.main:lifespan:42 - 正在关闭抖音分析浏览器...
2025-07-10 17:30:04 | INFO     | app.main:lifespan:44 - 抖音分析浏览器已关闭
2025-07-10 17:30:04 | INFO     | app.main:lifespan:48 - 抖音推送下载app_env关闭成功
2025-07-10 17:30:06 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:30:06 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:30:06 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPIProject\data
2025-07-10 17:30:06 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPIProject/data/db.sqlite3
2025-07-10 17:30:06 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-10 17:30:06 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-10 17:30:06 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-10 17:33:23 | INFO     | app.main:lifespan:30 - 正在关闭数据库引擎...
2025-07-10 17:33:23 | INFO     | app.main:lifespan:32 - 数据库引擎已关闭
2025-07-10 17:33:23 | INFO     | app.main:lifespan:42 - 正在关闭抖音分析浏览器...
2025-07-10 17:33:23 | INFO     | app.main:lifespan:44 - 抖音分析浏览器已关闭
2025-07-10 17:33:23 | INFO     | app.main:lifespan:48 - 抖音推送下载app_env关闭成功
2025-07-10 17:33:25 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:33:25 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:33:25 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPIProject\data
2025-07-10 17:33:25 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPIProject/data/db.sqlite3
2025-07-10 17:33:25 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-10 17:33:25 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-10 17:33:25 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-10 17:33:32 | SUCCESS  | app.core.init_data:init_roles:46 - 角色初始化完成 - 角色 创建: 0, 删除: 0；
2025-07-10 17:33:32 | ERROR    | app.core.init_data:init_permissions:108 - 初始化权限失败: 'str' object has no attribute 'description'
2025-07-10 17:33:32 | ERROR    | app.api.v1.auth.permissions:init_roles_permissions:54 - 初始化角色、权限失败: 'str' object has no attribute 'description'
2025-07-10 17:38:24 | INFO     | app.main:lifespan:30 - 正在关闭数据库引擎...
2025-07-10 17:38:24 | INFO     | app.main:lifespan:32 - 数据库引擎已关闭
2025-07-10 17:38:24 | INFO     | app.main:lifespan:42 - 正在关闭抖音分析浏览器...
2025-07-10 17:38:24 | INFO     | app.main:lifespan:44 - 抖音分析浏览器已关闭
2025-07-10 17:38:24 | INFO     | app.main:lifespan:48 - 抖音推送下载app_env关闭成功
2025-07-10 17:38:26 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:38:26 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:38:26 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPIProject\data
2025-07-10 17:38:26 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPIProject/data/db.sqlite3
2025-07-10 17:38:26 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-10 17:38:26 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-10 17:38:26 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-10 17:43:24 | INFO     | app.main:lifespan:30 - 正在关闭数据库引擎...
2025-07-10 17:43:24 | INFO     | app.main:lifespan:32 - 数据库引擎已关闭
2025-07-10 17:43:24 | INFO     | app.main:lifespan:42 - 正在关闭抖音分析浏览器...
2025-07-10 17:43:24 | INFO     | app.main:lifespan:44 - 抖音分析浏览器已关闭
2025-07-10 17:43:24 | INFO     | app.main:lifespan:48 - 抖音推送下载app_env关闭成功
2025-07-10 17:43:26 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:43:26 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:43:26 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPIProject\data
2025-07-10 17:43:26 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPIProject/data/db.sqlite3
2025-07-10 17:43:26 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-10 17:43:26 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-10 17:43:26 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-10 17:44:20 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:44:22 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:44:22 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-10 17:44:22 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPIProject\data
2025-07-10 17:44:22 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPIProject/data/db.sqlite3
2025-07-10 17:44:22 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-10 17:44:22 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-10 17:44:22 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-10 17:44:26 | SUCCESS  | app.core.init_data:init_roles:46 - 角色初始化完成 - 角色 创建: 0, 删除: 0；
2025-07-10 17:44:26 | SUCCESS  | app.core.init_data:init_permissions:103 - 权限初始化完成 - 权限 创建: 0, 删除: 0
2025-07-10 17:44:26 | SUCCESS  | app.api.v1.auth.permissions:init_roles_permissions:50 - 基本角色、权限初始化成功
2025-07-11 08:47:14 | INFO     | app.main:lifespan:30 - 正在关闭数据库引擎...
2025-07-11 08:47:14 | INFO     | app.main:lifespan:32 - 数据库引擎已关闭
2025-07-11 08:47:14 | INFO     | app.main:lifespan:42 - 正在关闭抖音分析浏览器...
2025-07-11 08:47:14 | INFO     | app.main:lifespan:44 - 抖音分析浏览器已关闭
2025-07-11 08:47:14 | INFO     | app.main:lifespan:48 - 抖音推送下载app_env关闭成功
2025-07-11 08:47:16 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-11 08:47:16 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-11 08:47:16 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPIProject\data
2025-07-11 08:47:16 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPIProject/data/db.sqlite3
2025-07-11 08:47:16 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-11 08:47:16 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-11 08:47:16 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-11 08:47:26 | ERROR    | app.core.init_data:init_admin_role_permission:137 - 角色权限初始化失败: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-11 08:47:26 | ERROR    | app.api.v1.auth.permissions:init_admin_permissions:69 - admin权限初始化失败: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-11 08:47:27 | ERROR    | app.core.init_data:init_admin_role_permission:137 - 角色权限初始化失败: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-11 08:47:27 | ERROR    | app.api.v1.auth.permissions:init_admin_permissions:69 - admin权限初始化失败: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-11 08:51:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-11 08:51:08 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-11 08:51:08 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-11 08:51:08 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPIProject\data
2025-07-11 08:51:08 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPIProject/data/db.sqlite3
2025-07-11 08:51:08 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-11 08:51:08 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-11 08:51:08 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-11 08:53:47 | INFO     | app.main:lifespan:30 - 正在关闭数据库引擎...
2025-07-11 08:53:47 | INFO     | app.main:lifespan:32 - 数据库引擎已关闭
2025-07-11 08:53:47 | INFO     | app.main:lifespan:42 - 正在关闭抖音分析浏览器...
2025-07-11 08:53:47 | INFO     | app.main:lifespan:44 - 抖音分析浏览器已关闭
2025-07-11 08:53:47 | INFO     | app.main:lifespan:48 - 抖音推送下载app_env关闭成功
2025-07-11 08:53:49 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-11 08:53:49 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-11 08:53:49 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPIProject\data
2025-07-11 08:53:49 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPIProject/data/db.sqlite3
2025-07-11 08:53:49 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-11 08:53:49 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-11 08:53:49 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-11 08:54:09 | SUCCESS  | app.api.v1.auth.permissions:init_admin_permissions:65 - admin权限初始化成功
2025-07-11 08:54:21 | SUCCESS  | app.api.v1.auth.permissions:init_admin_permissions:65 - admin权限初始化成功
2025-07-11 08:56:06 | INFO     | app.main:lifespan:30 - 正在关闭数据库引擎...
2025-07-11 08:56:06 | INFO     | app.main:lifespan:32 - 数据库引擎已关闭
2025-07-11 08:56:06 | INFO     | app.main:lifespan:42 - 正在关闭抖音分析浏览器...
2025-07-11 08:56:06 | INFO     | app.main:lifespan:44 - 抖音分析浏览器已关闭
2025-07-11 08:56:06 | INFO     | app.main:lifespan:48 - 抖音推送下载app_env关闭成功
2025-07-11 08:56:08 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-11 08:56:08 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPIProject\logs\app.log
2025-07-11 08:56:08 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPIProject\data
2025-07-11 08:56:08 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPIProject/data/db.sqlite3
2025-07-11 08:56:08 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-11 08:56:08 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-11 08:56:08 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-15 08:50:29 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-15 08:50:31 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-15 08:50:31 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-15 08:50:31 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-15 08:50:31 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-15 08:50:31 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-15 08:50:31 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-15 08:50:31 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-15 08:53:03 | INFO     | app.main:lifespan:30 - 正在关闭数据库引擎...
2025-07-15 08:53:03 | INFO     | app.main:lifespan:32 - 数据库引擎已关闭
2025-07-15 08:53:03 | INFO     | app.main:lifespan:42 - 正在关闭抖音分析浏览器...
2025-07-15 08:53:03 | INFO     | app.main:lifespan:44 - 抖音分析浏览器已关闭
2025-07-15 08:53:03 | INFO     | app.main:lifespan:48 - 抖音推送下载app_env关闭成功
2025-07-15 08:53:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-15 08:53:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-15 08:53:05 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-15 08:53:05 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-15 08:53:05 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-15 08:53:05 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-15 08:53:05 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-15 08:53:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-15 08:53:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-15 08:53:07 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-15 08:53:07 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-15 08:53:07 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-15 08:53:07 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-15 08:53:07 | SUCCESS  | app.main:lifespan:26 - 抖音推送下载app_env启动成功
2025-07-15 10:29:24 | INFO     | app.main:lifespan:30 - 正在关闭数据库引擎...
2025-07-15 10:29:24 | INFO     | app.main:lifespan:32 - 数据库引擎已关闭
2025-07-15 10:29:24 | INFO     | app.main:lifespan:42 - 正在关闭抖音分析浏览器...
2025-07-15 10:29:24 | INFO     | app.main:lifespan:44 - 抖音分析浏览器已关闭
2025-07-15 10:29:24 | INFO     | app.main:lifespan:48 - 抖音推送下载app_env关闭成功
2025-07-17 11:38:33 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 11:38:34 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 11:38:34 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 11:38:34 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 11:38:34 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 11:38:34 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 11:38:34 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 11:38:34 | ERROR    | app.db.redis:init_redis:35 - Redis connection error: AbstractConnection.__init__() got an unexpected keyword argument 'timeout'
2025-07-17 11:38:34 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 11:43:31 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 11:43:31 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 11:43:31 | INFO     | app.main:lifespan:46 - 正在关闭抖音分析浏览器...
2025-07-17 11:43:31 | INFO     | app.main:lifespan:48 - 抖音分析浏览器已关闭
2025-07-17 11:43:31 | INFO     | app.main:lifespan:52 - 抖音推送下载app_env关闭成功
2025-07-17 11:43:33 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 11:43:33 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 11:43:33 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 11:43:33 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 11:43:33 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 11:43:33 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 11:43:54 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 11:43:54 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 15:14:06 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 15:14:06 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 15:14:06 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 15:14:06 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 15:14:06 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 15:14:06 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 15:14:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:14:08 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:14:08 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 15:14:08 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 15:14:08 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 15:14:08 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 15:14:29 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 15:14:29 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 15:14:44 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:14:45 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:14:45 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:14:45 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 15:14:45 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 15:14:45 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 15:14:45 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 15:15:07 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 15:15:07 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 15:16:53 | INFO     | app.core.auth:verify_user:126 - 验证用户: heygo
2025-07-17 15:16:53 | ERROR    | app.core.auth:verify_user:134 - 验证用户时出错: When initializing mapper Mapper[ApiPermission(api_permissions)], expression 'UserToken' failed to locate a name ('UserToken'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.ApiPermission'> class after both dependent classes have been defined.
2025-07-17 15:17:33 | INFO     | app.core.auth:verify_user:126 - 验证用户: heygo
2025-07-17 15:17:33 | ERROR    | app.core.auth:verify_user:134 - 验证用户时出错: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ApiPermission(api_permissions)]'. Original exception was: When initializing mapper Mapper[ApiPermission(api_permissions)], expression 'UserToken' failed to locate a name ('UserToken'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.ApiPermission'> class after both dependent classes have been defined.
2025-07-17 15:19:43 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 15:19:43 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 15:19:43 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 15:19:43 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 15:19:43 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 15:19:43 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 15:19:45 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:19:45 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:19:45 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 15:19:45 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 15:19:45 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 15:19:45 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 15:20:06 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 15:20:06 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 15:24:19 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 15:24:19 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 15:24:19 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 15:24:19 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 15:24:19 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 15:24:19 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 15:24:21 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:24:21 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:24:21 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 15:24:21 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 15:24:21 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 15:24:21 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 15:24:42 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 15:24:42 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 15:29:20 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 15:29:20 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 15:29:20 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 15:29:20 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 15:29:20 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 15:29:20 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 15:29:22 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:29:22 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:29:22 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 15:29:22 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 15:29:22 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 15:29:22 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 15:29:43 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 15:29:43 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 15:30:42 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 15:30:42 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 15:30:42 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 15:30:42 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 15:30:42 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 15:30:42 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 15:30:44 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:30:46 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:30:46 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:30:46 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 15:30:46 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 15:30:46 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 15:30:46 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 15:31:07 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 15:31:07 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 15:33:23 | ERROR    | app.api.v1.auth.login:debug_list_users:199 - 获取用户列表失败: When initializing mapper Mapper[ApiPermission(api_permissions)], expression 'UserToken' failed to locate a name ('UserToken'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.ApiPermission'> class after both dependent classes have been defined.
2025-07-17 15:34:05 | ERROR    | app.api.v1.auth.login:debug_test_password:232 - 密码测试失败: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ApiPermission(api_permissions)]'. Original exception was: When initializing mapper Mapper[ApiPermission(api_permissions)], expression 'UserToken' failed to locate a name ('UserToken'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.ApiPermission'> class after both dependent classes have been defined.
2025-07-17 15:38:55 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 15:38:55 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 15:38:55 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 15:38:55 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 15:38:55 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 15:38:55 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 15:38:57 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:38:57 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:38:57 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 15:38:57 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 15:38:57 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 15:38:57 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 15:39:18 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 15:39:18 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 15:39:18 | INFO     | app.core.auth:verify_password:97 - plain: Heytime01!, hashed: $2b$12$JZZD/JyiK2tXBfLMxiFKq.oGoDzncNiSNPNR9QQXPjTzJxbhcbriG
2025-07-17 15:39:52 | INFO     | app.core.auth:verify_password:97 - plain: Heytime01!, hashed: $2b$12$JZZD/JyiK2tXBfLMxiFKq.oGoDzncNiSNPNR9QQXPjTzJxbhcbriG
2025-07-17 15:40:22 | INFO     | app.core.auth:verify_user:126 - 验证用户: heygo
2025-07-17 15:40:22 | INFO     | app.core.auth:verify_user:128 - 获取用户: User(id=1, username=heygo)
2025-07-17 15:40:22 | INFO     | app.core.auth:verify_password:97 - plain: Heytime01!, hashed: $2b$12$JZZD/JyiK2tXBfLMxiFKq.oGoDzncNiSNPNR9QQXPjTzJxbhcbriG
2025-07-17 15:40:22 | ERROR    | app.repositories.token_repository:store_refresh_token:77 - Failed to store refresh token for heygo: Error while reading from stablediffusion.heygo.cn:63798 : (10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)
2025-07-17 15:40:22 | WARNING  | app.api.v1.auth.login:login:51 - Failed to store refresh token for user: heygo
2025-07-17 15:40:22 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 15:40:22 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 15:40:22 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 15:40:22 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 15:40:22 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 15:40:22 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 15:40:24 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:40:24 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:40:24 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 15:40:24 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 15:40:24 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 15:40:24 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 15:40:45 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 15:40:45 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 15:45:23 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 15:45:23 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 15:45:23 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 15:45:23 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 15:45:23 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 15:45:23 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 15:45:24 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:45:24 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:45:24 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 15:45:24 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 15:45:24 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 15:45:24 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 15:45:46 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 15:45:46 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 15:50:23 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 15:50:23 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 15:50:23 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 15:50:23 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 15:50:23 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 15:50:23 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 15:50:25 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:50:25 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:50:25 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 15:50:25 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 15:50:25 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 15:50:25 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 15:50:47 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 15:50:47 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 15:55:24 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 15:55:24 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 15:55:24 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 15:55:24 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 15:55:24 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 15:55:24 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 15:55:25 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:55:25 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:55:26 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 15:55:26 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 15:55:26 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 15:55:26 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 15:55:47 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 15:55:47 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 15:59:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:59:09 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:59:09 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 15:59:09 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 15:59:09 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 15:59:09 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 15:59:09 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 15:59:30 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 15:59:30 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 16:00:28 | ERROR    | app.repositories.token_repository:list_all_tokens:211 - Failed to list refresh tokens: Error while reading from stablediffusion.heygo.cn:63798 : (10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)
2025-07-17 16:01:24 | INFO     | app.core.auth:verify_user:126 - 验证用户: heygo
2025-07-17 16:01:24 | INFO     | app.core.auth:verify_user:128 - 获取用户: User(id=1, username=heygo)
2025-07-17 16:01:24 | INFO     | app.core.auth:verify_password:97 - plain: Heytime01!, hashed: $2b$12$JZZD/JyiK2tXBfLMxiFKq.oGoDzncNiSNPNR9QQXPjTzJxbhcbriG
2025-07-17 16:01:46 | INFO     | app.repositories.token_repository:store_refresh_token:72 - Refresh token stored - key: refresh_token:cfb9baea-9488-46ac-8dc8-52d17ccf018d, sub: heygo, expires in 1295999.7300629616 seconds
2025-07-17 16:04:05 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 16:04:05 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 16:04:05 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 16:04:05 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 16:04:05 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 16:04:05 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 16:04:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 16:04:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 16:04:08 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 16:04:08 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 16:04:08 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 16:04:08 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 16:04:30 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 16:04:30 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 16:09:05 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 16:09:05 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 16:09:05 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 16:09:05 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 16:09:05 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 16:09:05 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 16:09:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 16:09:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 16:09:07 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 16:09:07 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 16:09:07 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 16:09:07 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 16:09:29 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 16:09:29 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 16:18:44 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 16:18:44 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 16:18:44 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 16:18:44 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 16:18:44 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 16:18:44 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 16:18:46 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 16:18:46 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 16:18:46 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 16:18:46 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 16:18:46 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 16:18:46 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 16:18:52 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 16:18:54 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 16:18:54 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 16:18:54 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 16:18:54 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 16:18:54 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 16:18:54 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 16:19:15 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 16:19:15 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 16:23:50 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 16:23:50 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 16:23:50 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 16:23:50 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 16:23:50 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 16:23:50 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 16:23:52 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 16:23:52 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 16:23:52 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 16:23:52 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 16:23:52 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 16:23:52 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 16:24:13 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 16:24:13 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 16:28:50 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 16:28:50 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 16:28:50 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 16:28:50 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 16:28:50 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 16:28:50 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 16:28:52 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 16:28:52 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 16:28:52 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 16:28:52 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 16:28:52 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 16:28:52 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 16:29:14 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 16:29:14 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 17:26:11 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 17:26:11 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 17:26:11 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 17:26:11 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 17:26:11 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 17:26:11 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 17:26:13 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 17:26:13 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 17:26:13 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 17:26:13 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 17:26:13 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 17:26:13 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 17:26:35 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 17:26:35 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 18:06:31 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 18:06:31 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 18:06:31 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 18:06:31 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 18:06:31 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 18:06:31 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 18:06:35 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 18:06:36 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 18:06:36 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 18:06:36 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 18:06:36 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 18:06:36 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 18:06:36 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 18:06:57 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 18:06:57 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 18:07:55 | INFO     | app.core.auth:verify_user:126 - 验证用户: heygo
2025-07-17 18:07:55 | INFO     | app.core.auth:verify_user:128 - 获取用户: User(id=1, username=heygo)
2025-07-17 18:07:55 | INFO     | app.core.auth:verify_password:97 - plain: Heytime01!, hashed: $2b$12$JZZD/JyiK2tXBfLMxiFKq.oGoDzncNiSNPNR9QQXPjTzJxbhcbriG
2025-07-17 18:07:56 | INFO     | app.repositories.token_repository:store_refresh_token:72 - Refresh token stored - key: refresh_token:bee09db4-bb05-4d9a-a463-2b57b9d7bd7b, sub: heygo, expires in 1295999.7809329033 seconds
2025-07-17 18:11:32 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 18:11:32 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 18:11:32 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 18:11:32 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 18:11:32 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 18:11:32 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 18:11:34 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 18:11:34 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 18:11:34 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 18:11:34 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 18:11:34 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 18:11:34 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 18:11:56 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 18:11:56 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 18:16:32 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 18:16:32 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 18:16:32 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 18:16:32 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 18:16:32 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 18:16:32 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 18:16:34 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 18:16:34 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 18:16:34 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 18:16:34 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 18:16:34 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 18:16:34 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 18:16:55 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 18:16:55 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 18:21:33 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 18:21:33 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 18:21:33 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 18:21:33 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 18:21:33 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 18:21:33 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 18:21:35 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 18:21:35 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 18:21:35 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 18:21:35 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 18:21:35 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 18:21:35 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 18:21:56 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 18:21:56 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 20:17:49 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 20:17:49 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 20:17:49 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 20:17:49 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 20:17:49 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 20:17:49 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 20:17:51 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:17:51 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:17:51 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 20:17:51 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 20:17:51 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 20:17:51 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 20:17:55 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:17:57 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:17:57 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:17:57 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 20:17:57 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 20:17:57 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 20:17:57 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 20:18:18 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 20:18:18 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 20:22:54 | INFO     | app.repositories.token_repository:revoke_refresh_token:147 - Refresh token revoked for user: heygo
2025-07-17 20:22:54 | INFO     | app.repositories.token_repository:store_refresh_token:72 - Refresh token stored - key: refresh_token:0f098911-f0b7-44b8-8990-1f59ac03e3d8, sub: heygo, expires in 1295999.776350975 seconds
2025-07-17 20:22:54 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 20:22:54 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 20:22:54 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 20:22:54 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 20:22:54 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 20:22:54 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 20:22:56 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:22:56 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:22:56 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 20:22:56 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 20:22:56 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 20:22:56 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 20:23:17 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 20:23:17 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 20:24:28 | ERROR    | app.repositories.token_repository:get_stored_refresh_token:104 - Failed to get refresh token for 0f098911-f0b7-44b8-8990-1f59ac03e3d8: Error while reading from stablediffusion.heygo.cn:63798 : (10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)
2025-07-17 20:32:54 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 20:32:54 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 20:32:54 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 20:32:54 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 20:32:54 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 20:32:54 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 20:32:56 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:32:56 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:32:56 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 20:32:56 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 20:32:56 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 20:32:56 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 20:33:18 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 20:33:18 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 20:37:55 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 20:37:55 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 20:37:55 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 20:37:55 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 20:37:55 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 20:37:55 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 20:37:56 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:37:56 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:37:57 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 20:37:57 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 20:37:57 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 20:37:57 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 20:38:18 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 20:38:18 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 20:39:39 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 20:39:39 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 20:39:39 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 20:39:39 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 20:39:39 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 20:39:39 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 20:39:41 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:39:42 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:39:42 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:39:43 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 20:39:43 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 20:39:43 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 20:39:43 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 20:40:04 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 20:40:04 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 20:40:54 | INFO     | app.api.v1.auth.token:validate_refresh_token:198 - 开始验证 refresh token，token 长度: 229
2025-07-17 20:40:54 | INFO     | app.api.v1.auth.token:validate_refresh_token:211 - Token 验证成功: username=heygo, jti=0f098911-f0b7-44b8-8990-1f59ac03e3d8
2025-07-17 20:41:09 | INFO     | app.repositories.token_repository:revoke_refresh_token:151 - Refresh token revoked for user: heygo
2025-07-17 20:41:09 | INFO     | app.repositories.token_repository:store_refresh_token:72 - Refresh token stored - key: refresh_token:9b1dfc6f-964d-4243-bb11-69016b432cef, sub: heygo, expires in 1295998.8995110989 seconds
2025-07-17 20:44:40 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 20:44:40 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 20:44:40 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 20:44:40 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 20:44:40 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 20:44:40 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 20:44:42 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:44:42 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:44:42 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 20:44:42 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 20:44:42 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 20:44:42 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 20:45:03 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 20:45:03 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 20:45:15 | INFO     | app.api.v1.auth.token:validate_refresh_token:198 - 开始验证 refresh token，token 长度: 229
2025-07-17 20:45:15 | INFO     | app.api.v1.auth.token:validate_refresh_token:211 - Token 验证成功: username=heygo, jti=9b1dfc6f-964d-4243-bb11-69016b432cef
2025-07-17 20:45:15 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 20:45:15 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 20:45:15 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 20:45:15 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 20:45:15 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 20:45:15 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 20:45:17 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:45:17 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:45:17 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 20:45:17 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 20:45:17 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 20:45:17 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 20:45:38 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 20:45:38 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 20:45:39 | INFO     | app.repositories.token_repository:list_all_tokens:211 - Found 2 active refresh tokens
2025-07-17 20:46:35 | INFO     | app.api.v1.auth.token:get_refresh_token_info:70 - 获取 refresh token 信息: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJoZXlnbyIsImV4cCI6MTc1NDA1MjA2OCwidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI3NTYwNjgsImp0aSI6IjliMWRmYzZmLTk2NGQtNDI0My1iYjExLTY5MDE2YjQzMmNlZiJ9.RP5WmBzJDbenlOiXUxOznbZMRC_-SyOOIhZFtqWjTio
2025-07-17 20:55:15 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 20:55:15 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 20:55:15 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 20:55:15 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 20:55:15 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 20:55:15 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 20:55:17 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:55:17 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 20:55:18 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 20:55:18 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 20:55:18 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 20:55:18 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 20:55:39 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 20:55:39 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 21:33:40 | ERROR    | app.repositories.token_repository:get_stored_refresh_token:106 - Redis connection error for JTI 9b1dfc6f-964d-4243-bb11-69016b432cef: Error while reading from stablediffusion.heygo.cn:63798 : (10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)
2025-07-17 21:34:29 | INFO     | app.repositories.token_repository:revoke_refresh_token:151 - Refresh token revoked for user: heygo
2025-07-17 21:34:29 | INFO     | app.repositories.token_repository:store_refresh_token:72 - Refresh token stored - key: refresh_token:db5da1f1-5102-40e0-a703-839d6f08f437, sub: heygo, expires in 1295999.843800068 seconds
2025-07-17 21:36:38 | INFO     | app.api.v1.auth.token:validate_refresh_token:198 - 开始验证 refresh token，token 长度: 229
2025-07-17 21:36:38 | ERROR    | app.repositories.token_repository:get_stored_refresh_token:106 - Redis connection error for JTI db5da1f1-5102-40e0-a703-839d6f08f437: Error while reading from stablediffusion.heygo.cn:63798 : (10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)
2025-07-17 21:36:38 | WARNING  | app.api.v1.auth.token:validate_refresh_token:221 - Token 验证失败 (HTTPException): Invalid refresh token
2025-07-17 21:36:42 | INFO     | app.api.v1.auth.token:validate_refresh_token:198 - 开始验证 refresh token，token 长度: 229
2025-07-17 21:37:03 | WARNING  | app.api.v1.auth.token:validate_refresh_token:221 - Token 验证失败 (HTTPException): Invalid refresh token
2025-07-17 21:37:37 | INFO     | app.api.v1.auth.token:validate_refresh_token:198 - 开始验证 refresh token，token 长度: 229
2025-07-17 21:37:37 | ERROR    | app.repositories.token_repository:get_stored_refresh_token:106 - Redis connection error for JTI db5da1f1-5102-40e0-a703-839d6f08f437: Error while reading from stablediffusion.heygo.cn:63798 : (10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)
2025-07-17 21:37:37 | WARNING  | app.api.v1.auth.token:validate_refresh_token:221 - Token 验证失败 (HTTPException): Invalid refresh token
2025-07-17 21:39:10 | INFO     | app.repositories.token_repository:revoke_all_user_tokens:250 - Revoked 1 refresh tokens for user: heygo
2025-07-17 22:05:25 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 22:05:25 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 22:05:25 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 22:05:25 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 22:05:25 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 22:05:25 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 22:05:27 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 22:05:27 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 22:05:27 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 22:05:27 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 22:05:27 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 22:05:27 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 22:05:48 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 22:05:48 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 22:06:53 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 22:06:54 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 22:06:54 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 22:06:55 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 22:06:55 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 22:06:55 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 22:06:55 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 22:07:16 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 22:07:16 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 22:07:52 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 22:07:52 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 22:07:52 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 22:07:52 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 22:07:52 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 22:07:52 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 22:07:54 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 22:07:54 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 22:07:54 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 22:07:54 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 22:07:54 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 22:07:54 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 22:08:04 | ERROR    | app.db.redis:init_redis:52 - Redis connection/timeout error: Timeout connecting to server
2025-07-17 22:08:04 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 22:09:33 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 22:09:34 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 22:09:34 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 22:09:34 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 22:09:34 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 22:09:34 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 22:09:34 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 22:09:44 | ERROR    | app.db.redis:init_redis:52 - Redis connection/timeout error: Timeout connecting to server
2025-07-17 22:09:44 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 22:14:28 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 22:14:28 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 22:14:28 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 22:14:28 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 22:14:28 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 22:14:30 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 22:14:30 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 22:14:30 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 22:14:30 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 22:14:30 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 22:14:30 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 22:14:34 | ERROR    | app.db.redis:init_redis:76 - Redis connection/timeout error: Error Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379) connecting to localhost:6379.
2025-07-17 22:14:34 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 23:40:00 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 23:40:00 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 23:40:00 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 23:40:00 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 23:40:00 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 23:40:03 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 23:40:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 23:40:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 23:40:05 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 23:40:05 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 23:40:05 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 23:40:05 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 23:40:09 | ERROR    | app.db.redis:init_redis:76 - Redis connection/timeout error: Error Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379) connecting to localhost:6379.
2025-07-17 23:40:09 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 23:42:07 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 23:42:07 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 23:42:07 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 23:42:07 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 23:42:07 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 23:42:09 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 23:42:09 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 23:42:10 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 23:42:10 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 23:42:10 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 23:42:10 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 23:42:14 | ERROR    | app.db.redis:init_redis:76 - Redis connection/timeout error: Error Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379) connecting to localhost:6379.
2025-07-17 23:42:14 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 23:42:15 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 23:42:16 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 23:42:16 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 23:42:16 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 23:42:16 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 23:42:16 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 23:42:20 | ERROR    | app.db.redis:init_redis:76 - Redis connection/timeout error: Error Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379) connecting to localhost:6379.
2025-07-17 23:42:20 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 23:43:08 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 23:43:10 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 23:43:10 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 23:43:10 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 23:43:10 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 23:43:10 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 23:43:10 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 23:43:31 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 23:43:31 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-17 23:43:41 | INFO     | app.api.v1.auth.login:logout:93 - Refresh token revoked for user: heygo
2025-07-17 23:43:41 | INFO     | app.api.v1.auth.login:logout:108 - Logout completed for user: heygo
2025-07-17 23:44:13 | ERROR    | app.api.v1.auth.token:test_redis_connection:316 - Redis connection test failed: Error while reading from stablediffusion.heygo.cn:63798 : (10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)
2025-07-17 23:44:33 | INFO     | app.core.auth:verify_user:126 - 验证用户: heygo
2025-07-17 23:44:33 | INFO     | app.core.auth:verify_user:128 - 获取用户: User(id=1, username=heygo)
2025-07-17 23:44:33 | INFO     | app.core.auth:verify_password:97 - plain: Heytime01!, hashed: $2b$12$JZZD/JyiK2tXBfLMxiFKq.oGoDzncNiSNPNR9QQXPjTzJxbhcbriG
2025-07-17 23:44:55 | INFO     | app.repositories.token_repository:store_refresh_token:144 - Refresh token stored - key: refresh_token:11b57abe-a422-42a7-a538-2bf1fdeaa892, sub: heygo, expires in 1295999.2945930958 seconds
2025-07-17 23:45:30 | INFO     | app.api.v1.auth.token:validate_refresh_token:198 - 开始验证 refresh token，token 长度: 229
2025-07-17 23:45:30 | INFO     | app.api.v1.auth.token:validate_refresh_token:211 - Token 验证成功: username=heygo, jti=11b57abe-a422-42a7-a538-2bf1fdeaa892
2025-07-17 23:45:38 | INFO     | app.api.v1.auth.token:get_refresh_token_info:70 - 获取 refresh token 信息: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJoZXlnbyIsImV4cCI6MTc1NDA2MzA3MywidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI3NjcwNzMsImp0aSI6IjExYjU3YWJlLWE0MjItNDJhNy1hNTM4LTJiZjFmZGVhYTg5MiJ9.wQWxD2BUU0oyY1OamElsYy-__-S9nQI6uNPw3qWJa-E
2025-07-17 23:48:06 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-17 23:48:06 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-17 23:48:06 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-17 23:48:06 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-17 23:48:06 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-17 23:48:06 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-17 23:48:08 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 23:48:08 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-17 23:48:08 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-17 23:48:08 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-17 23:48:08 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-17 23:48:08 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-17 23:48:30 | INFO     | app.db.redis:init_redis:41 - Redis connection successful
2025-07-17 23:48:30 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 08:56:20 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 08:56:20 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 08:56:20 | INFO     | app.db.redis:close_redis:68 - Redis connection closed
2025-07-18 08:56:20 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 08:56:20 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 08:56:20 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 08:56:22 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 08:56:22 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 08:56:22 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 08:56:22 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 08:56:22 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 08:56:22 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 08:56:22 | ERROR    | app.db.redis:init_redis:58 - Redis connection/timeout error: Connection closed by server.
2025-07-18 08:56:22 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 10:11:50 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 10:11:50 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 10:11:50 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 10:11:50 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 10:11:50 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 10:19:44 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 10:19:45 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 10:19:45 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 10:19:46 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 10:19:46 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 10:19:46 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 10:19:46 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 10:19:46 | INFO     | app.db.redis:init_redis:50 - Redis connection successful
2025-07-18 10:19:46 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 10:20:18 | INFO     | app.api.v1.auth.token:get_refresh_token_info:70 - 获取 refresh token 信息: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJoZXlnbyIsImV4cCI6MTc1NDA2MzA3MywidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI3NjcwNzMsImp0aSI6IjExYjU3YWJlLWE0MjItNDJhNy1hNTM4LTJiZjFmZGVhYTg5MiJ9.wQWxD2BUU0oyY1OamElsYy-__-S9nQI6uNPw3qWJa-E
2025-07-18 10:21:23 | INFO     | app.api.v1.auth.login:logout:93 - Refresh token revoked for user: heygo
2025-07-18 10:21:23 | INFO     | app.api.v1.auth.login:logout:108 - Logout completed for user: heygo
2025-07-18 10:21:25 | INFO     | app.api.v1.auth.login:logout:100 - No refresh token found during logout
2025-07-18 10:21:25 | INFO     | app.api.v1.auth.login:logout:108 - Logout completed for user: unknown
2025-07-18 10:21:35 | ERROR    | app.api.v1.auth.token:test_redis_connection:316 - Redis connection test failed: 'str' object has no attribute 'decode'
2025-07-18 10:22:01 | INFO     | app.core.auth:verify_user:126 - 验证用户: heygo
2025-07-18 10:22:01 | INFO     | app.core.auth:verify_user:128 - 获取用户: User(id=1, username=heygo)
2025-07-18 10:22:01 | INFO     | app.core.auth:verify_password:97 - plain: Heytime01!, hashed: $2b$12$JZZD/JyiK2tXBfLMxiFKq.oGoDzncNiSNPNR9QQXPjTzJxbhcbriG
2025-07-18 10:22:01 | INFO     | app.repositories.token_repository:store_refresh_token:144 - Refresh token stored - key: refresh_token:992dde8e-ad33-48da-bd5b-5b057b7631d7, sub: heygo, expires in 1295999.4279139042 seconds
2025-07-18 10:22:19 | INFO     | app.api.v1.auth.token:get_refresh_token_info:70 - 获取 refresh token 信息: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJoZXlnbyIsImV4cCI6MTc1NDEwMTMyMSwidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI4MDUzMjEsImp0aSI6Ijk5MmRkZThlLWFkMzMtNDhkYS1iZDViLTViMDU3Yjc2MzFkNyJ9.k2W6BcMlIXBx6CZZ__5FmcYCW4uWrtZHrRsUpnz_-CA
2025-07-18 10:26:21 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 10:26:21 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 10:26:21 | INFO     | app.db.redis:close_redis:77 - Redis connection closed
2025-07-18 10:26:21 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 10:26:21 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 10:26:21 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 10:26:22 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 10:26:22 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 10:26:22 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 10:26:22 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 10:26:22 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 10:26:22 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 10:26:32 | ERROR    | app.db.redis:init_redis:52 - Redis connection/timeout error: Timeout connecting to server
2025-07-18 10:26:32 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 10:27:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 10:27:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 10:27:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 10:27:07 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 10:27:07 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 10:27:07 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 10:27:07 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 10:27:17 | ERROR    | app.db.redis:init_redis:52 - Redis connection/timeout error: Timeout connecting to server
2025-07-18 10:27:17 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 10:32:04 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 10:32:04 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 10:32:04 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 10:32:04 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 10:32:04 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 10:32:06 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 10:32:06 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 10:32:06 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 10:32:06 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 10:32:06 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 10:32:06 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 10:32:08 | ERROR    | app.db.redis:init_redis:66 - Redis connection/timeout error: Error 10061 connecting to 127.0.0.1:16379. Connect call failed ('127.0.0.1', 16379).
2025-07-18 10:32:08 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 10:46:50 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 10:46:50 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 10:46:50 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 10:46:50 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 10:46:50 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 10:46:54 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 10:46:55 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 10:46:55 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 10:46:55 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 10:46:55 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 10:46:55 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 10:46:55 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 10:46:55 | INFO     | app.db.redis:init_redis:59 - Redis connection successful
2025-07-18 10:46:55 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 11:38:01 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 11:38:01 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 11:38:01 | INFO     | app.db.redis:close_redis:86 - Redis connection closed
2025-07-18 11:38:01 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 11:38:01 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 11:38:01 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 11:38:03 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 11:38:03 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 11:38:03 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 11:38:03 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 11:38:03 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 11:38:03 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 11:38:09 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 11:38:10 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 11:38:10 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 11:38:10 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 11:38:10 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 11:38:10 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 11:38:10 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 11:38:20 | ERROR    | app.db.redis:init_redis:52 - Redis connection/timeout error: Timeout connecting to server
2025-07-18 11:38:20 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 14:21:00 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 14:21:00 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 14:21:00 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 14:21:00 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 14:21:00 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 14:21:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:21:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:21:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:21:07 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 14:21:07 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 14:21:07 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 14:21:07 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 14:21:17 | ERROR    | app.db.redis:init_redis:52 - Redis connection/timeout error: Timeout connecting to server
2025-07-18 14:21:17 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 14:26:03 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 14:26:03 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 14:26:03 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 14:26:03 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 14:26:03 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 14:26:04 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:26:04 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:26:05 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 14:26:05 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 14:26:05 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 14:26:05 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 14:26:07 | ERROR    | app.db.redis:init_redis:52 - Redis connection/timeout error: Error 10061 connecting to 127.0.0.1:63798. Connect call failed ('127.0.0.1', 63798).
2025-07-18 14:26:07 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 14:29:46 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 14:29:46 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 14:29:46 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 14:29:46 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 14:29:46 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 14:29:48 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:29:50 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:29:50 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:29:50 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 14:29:50 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 14:29:50 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 14:29:50 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 14:34:54 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:34:56 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:34:56 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:34:56 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 14:34:56 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 14:34:56 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 14:34:56 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 14:34:58 | ERROR    | app.db.redis:init_redis:40 - Redis connection/timeout error: Error 10061 connecting to 127.0.0.1:16397. Connect call failed ('127.0.0.1', 16397).
2025-07-18 14:34:58 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 14:35:47 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 14:35:47 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 14:35:47 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 14:35:47 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 14:35:47 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 14:41:23 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:41:24 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:41:25 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:41:25 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 14:41:25 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 14:41:25 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 14:41:25 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 14:41:25 | ERROR    | app.db.redis:init_redis:24 - Redis connection pool not initialized
2025-07-18 14:41:25 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 14:42:27 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 14:42:27 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 14:42:27 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 14:42:27 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 14:42:27 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 14:42:29 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:42:29 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:42:29 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 14:42:29 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 14:42:29 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 14:42:29 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 14:42:31 | ERROR    | app.db.redis:init_redis:40 - Redis connection/timeout error: Error 10061 connecting to 127.0.0.1:16397. Connect call failed ('127.0.0.1', 16397).
2025-07-18 14:42:31 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 14:45:06 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:45:08 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:45:08 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:45:08 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 14:45:08 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 14:45:08 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 14:45:08 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 14:45:08 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 14:45:08 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 14:50:05 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 14:50:05 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 14:50:05 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 14:50:05 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 14:50:05 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 14:50:05 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 14:50:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:50:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:50:07 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 14:50:07 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 14:50:07 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 14:50:07 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 14:50:07 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 14:50:07 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 14:57:43 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 14:57:43 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 14:57:43 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 14:57:43 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 14:57:43 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 14:57:43 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 14:57:45 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:57:45 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:57:45 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 14:57:45 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 14:57:45 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 14:57:45 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 14:57:45 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 14:57:45 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 14:57:51 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:57:53 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:57:53 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 14:57:53 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 14:57:53 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 14:57:53 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 14:57:53 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 14:57:53 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 14:57:53 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 15:02:49 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 15:02:49 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 15:02:49 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 15:02:49 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 15:02:49 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 15:02:49 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 15:02:51 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:02:51 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:02:51 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 15:02:51 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 15:02:51 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 15:02:51 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 15:02:51 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 15:02:51 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 15:07:49 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 15:07:49 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 15:07:49 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 15:07:49 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 15:07:49 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 15:07:49 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 15:07:51 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:07:51 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:07:51 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 15:07:51 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 15:07:51 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 15:07:51 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 15:07:51 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 15:07:51 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 15:12:09 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 15:12:09 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 15:12:09 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 15:12:09 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 15:12:09 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 15:12:09 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 15:12:11 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:12:11 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:12:11 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 15:12:11 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 15:12:11 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 15:12:11 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 15:12:11 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 15:12:11 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 15:12:59 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 15:12:59 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 15:12:59 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 15:12:59 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 15:12:59 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 15:12:59 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 15:13:02 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:13:03 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:13:04 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:13:04 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 15:13:04 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 15:13:04 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 15:13:04 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 15:13:04 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 15:13:04 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 15:18:00 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 15:18:00 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 15:18:00 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 15:18:00 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 15:18:00 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 15:18:00 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 15:18:02 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:18:02 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:18:02 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 15:18:02 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 15:18:02 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 15:18:02 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 15:18:02 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 15:18:02 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 15:20:33 | INFO     | app.repositories.token_repository:list_all_tokens:192 - Found 1 active refresh tokens
2025-07-18 15:20:33 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 15:20:33 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 15:20:33 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 15:20:33 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 15:20:33 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 15:20:33 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 15:20:35 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:20:35 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:20:35 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 15:20:35 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 15:20:35 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 15:20:35 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 15:20:35 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 15:20:35 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 15:20:56 | INFO     | app.repositories.token_repository:list_all_tokens:195 - Found 1 active refresh tokens
2025-07-18 15:20:57 | INFO     | app.repositories.token_repository:list_all_tokens:195 - Found 1 active refresh tokens
2025-07-18 15:25:33 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 15:25:33 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 15:25:33 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 15:25:33 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 15:25:33 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 15:25:33 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 15:25:35 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:25:35 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:25:35 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 15:25:35 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 15:25:35 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 15:25:35 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 15:25:35 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 15:25:35 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 15:30:34 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 15:30:34 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 15:30:34 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 15:30:34 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 15:30:34 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 15:30:34 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 15:30:36 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:30:36 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:30:36 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 15:30:36 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 15:30:36 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 15:30:36 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 15:30:36 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 15:30:36 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 15:35:34 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 15:35:34 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 15:35:34 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 15:35:34 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 15:35:34 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 15:35:34 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 15:35:36 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:35:36 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:35:36 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 15:35:36 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 15:35:36 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 15:35:36 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 15:35:36 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 15:35:36 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 15:45:10 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 15:45:10 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 15:45:10 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 15:45:10 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 15:45:10 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 15:45:10 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 15:45:11 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:45:11 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 15:45:11 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 15:45:11 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 15:45:11 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 15:45:11 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 15:45:11 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 15:45:11 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 16:33:07 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 16:33:07 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 16:33:07 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 16:33:07 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 16:33:07 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 16:33:07 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 16:33:09 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:33:09 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:33:09 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 16:33:09 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 16:33:09 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 16:33:09 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 16:33:09 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 16:33:09 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 16:33:42 | INFO     | app.api.v1.auth.token:validate_refresh_token:187 - 开始验证 refresh token，token 长度: 229
2025-07-18 16:33:43 | INFO     | app.api.v1.auth.token:validate_refresh_token:201 - Token 验证成功: username=heygo, jti=992dde8e-ad33-48da-bd5b-5b057b7631d7
2025-07-18 16:33:43 | ERROR    | app.api.v1.auth.token:validate_refresh_token:220 - 验证 refresh token 失败 (Exception): 1 validation error for TokenValidationResponse
expires_seconds
  Input should be a valid integer, got a number with a fractional part [type=int_from_float, input_value=1273697.99552989, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/int_from_float
2025-07-18 16:34:30 | INFO     | app.api.v1.auth.token:validate_refresh_token:187 - 开始验证 refresh token，token 长度: 229
2025-07-18 16:34:30 | INFO     | app.api.v1.auth.token:validate_refresh_token:201 - Token 验证成功: username=heygo, jti=992dde8e-ad33-48da-bd5b-5b057b7631d7
2025-07-18 16:34:30 | ERROR    | app.api.v1.auth.token:validate_refresh_token:220 - 验证 refresh token 失败 (Exception): 1 validation error for TokenValidationResponse
expires_seconds
  Input should be a valid integer, got a number with a fractional part [type=int_from_float, input_value=1273650.8816990852, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/int_from_float
2025-07-18 16:36:14 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:36:16 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:36:16 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:36:16 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 16:36:16 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 16:36:16 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 16:36:16 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 16:36:16 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 16:36:16 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 16:36:17 | INFO     | app.api.v1.auth.token:validate_refresh_token:187 - 开始验证 refresh token，token 长度: 229
2025-07-18 16:36:17 | SUCCESS  | app.api.v1.auth.token:validate_refresh_token:201 - Token 验证成功: username=heygo, jti=992dde8e-ad33-48da-bd5b-5b057b7631d7
2025-07-18 16:54:55 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 16:54:55 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 16:54:55 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 16:54:55 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 16:54:55 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 16:54:55 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 16:54:58 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:54:59 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:54:59 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:54:59 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 16:54:59 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 16:54:59 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 16:54:59 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 16:54:59 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 16:54:59 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 16:55:20 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 16:55:20 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 16:55:20 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 16:55:20 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 16:55:20 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 16:55:20 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 16:55:23 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:55:24 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:55:24 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:55:24 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 16:55:24 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 16:55:24 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 16:55:24 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 16:55:24 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 16:55:24 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 16:55:33 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 16:55:33 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 16:55:33 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 16:55:33 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 16:55:33 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 16:55:33 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 16:55:35 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:55:37 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:55:37 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:55:37 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 16:55:37 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 16:55:37 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 16:55:37 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 16:55:37 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 16:55:37 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 16:57:16 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 16:57:16 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 16:57:16 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 16:57:16 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 16:57:16 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 16:57:16 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 16:57:17 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:57:17 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:57:17 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 16:57:17 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 16:57:17 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 16:57:17 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 16:57:17 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 16:57:17 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 16:58:04 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:58:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:58:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:58:05 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 16:58:05 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 16:58:05 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 16:58:05 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 16:58:05 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 16:58:05 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 16:58:23 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 16:58:23 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 16:58:23 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 16:58:23 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 16:58:23 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 16:58:23 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 16:58:25 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:58:25 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 16:58:25 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 16:58:25 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 16:58:25 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 16:58:25 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 16:58:25 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 16:58:25 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 16:58:27 | INFO     | app.api.v1.auth.token:get_access_token:173 - No access token found in Authorization header, trying cookie...
2025-07-18 16:58:28 | INFO     | app.api.v1.auth.token:get_access_token:173 - No access token found in Authorization header, trying cookie...
2025-07-18 16:58:46 | INFO     | app.api.v1.auth.token:get_access_token:173 - No access token found in Authorization header, trying cookie...
2025-07-18 16:58:48 | INFO     | app.api.v1.auth.token:get_access_token:173 - No access token found in Authorization header, trying cookie...
2025-07-18 16:58:48 | INFO     | app.api.v1.auth.token:get_access_token:173 - No access token found in Authorization header, trying cookie...
2025-07-18 16:59:15 | INFO     | app.api.v1.auth.token:get_access_token:173 - No access token found in Authorization header, trying cookie...
2025-07-18 16:59:16 | INFO     | app.api.v1.auth.token:get_access_token:173 - No access token found in Authorization header, trying cookie...
2025-07-18 16:59:17 | INFO     | app.api.v1.auth.token:get_access_token:173 - No access token found in Authorization header, trying cookie...
2025-07-18 16:59:19 | INFO     | app.api.v1.auth.token:get_access_token:173 - No access token found in Authorization header, trying cookie...
2025-07-18 16:59:20 | INFO     | app.api.v1.auth.token:get_access_token:173 - No access token found in Authorization header, trying cookie...
2025-07-18 16:59:20 | INFO     | app.api.v1.auth.token:get_access_token:173 - No access token found in Authorization header, trying cookie...
2025-07-18 17:08:03 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 17:08:03 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 17:08:03 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 17:08:03 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 17:08:03 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 17:08:03 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 17:08:04 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:08:04 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:08:05 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 17:08:05 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 17:08:05 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 17:08:05 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 17:08:05 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 17:08:05 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 17:15:30 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:15:32 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:15:32 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:15:32 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 17:15:32 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 17:15:32 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 17:15:32 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 17:15:32 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 17:15:32 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 17:15:35 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:15:35 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 2db194ea-1dae-4fbc-80d8-e5c1946a6869
2025-07-18 17:15:50 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:15:50 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 7f9cdf2c-cc41-4790-b49d-ac009371030e
2025-07-18 17:16:01 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:16:01 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: f4ee7f48-293b-419a-a120-ad86ea58b97b
2025-07-18 17:16:02 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:16:02 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: b7e126e5-336c-46fe-8b1f-959ab9405433
2025-07-18 17:16:04 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:16:04 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 2abb0de4-da29-4268-b07f-b1c89efdb7f1
2025-07-18 17:16:05 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:16:05 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 1b73cf40-be43-4484-9d66-b6608d7ed26e
2025-07-18 17:16:06 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:16:06 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: cd0931d2-083d-4db5-8ac1-80ed1de4cf44
2025-07-18 17:16:06 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:16:06 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 21c030c9-9335-40af-87d9-756c187c6d4f
2025-07-18 17:16:08 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:16:08 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 0bfe75da-079e-4f6c-845e-6b4f90b67e20
2025-07-18 17:16:09 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:16:09 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 854774ba-c58e-487d-95d0-1b817e59e9ba
2025-07-18 17:16:09 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:16:09 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: da7e9024-94f2-47ca-a260-8962f159731e
2025-07-18 17:16:10 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:16:10 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 55c78952-745e-4542-9d2a-ed6cc6f5f79a
2025-07-18 17:16:13 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:16:13 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: d86d1140-5b89-466b-bbd8-5b3b565c0535
2025-07-18 17:16:21 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:16:21 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 92961a24-9ca2-4643-98b2-068736a0f0bc
2025-07-18 17:27:41 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 17:27:41 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 17:27:41 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 17:27:41 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 17:27:41 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 17:27:41 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 17:27:42 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:27:42 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:27:42 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 17:27:42 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 17:27:42 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 17:27:42 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 17:27:42 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 17:27:42 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 17:27:45 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:27:46 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:27:46 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:27:46 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 17:27:46 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 17:27:46 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 17:27:46 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 17:27:46 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 17:27:46 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 17:28:22 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:28:22 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 93f76eb7-8ccf-4513-bbee-873fc95124ca
2025-07-18 17:28:44 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:28:44 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 8738c57d-4fff-47f4-8ab5-765ad57dbaeb
2025-07-18 17:28:49 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:28:49 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 0f1faa2b-239f-4ffa-b5fb-16dae3e8da8d
2025-07-18 17:28:51 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:28:51 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: d66c8c2f-82ca-420b-9de9-c6cf3afa018f
2025-07-18 17:29:09 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:29:09 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 2d62e271-57ee-4be2-99bc-c21a04ce9e57
2025-07-18 17:29:12 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:29:12 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 1d4a10d6-b03c-469b-b0f5-1d0d5663f738
2025-07-18 17:29:25 | INFO     | app.api.v1.auth.token:test_swagger_auth:401 - Swagger UI authentication test successful for user: heygo
2025-07-18 17:30:41 | INFO     | app.api.v1.auth.token:test_swagger_auth:401 - Swagger UI authentication test successful for user: heygo
2025-07-18 17:30:48 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:30:48 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 648cca10-3418-4935-a5f8-f7c914cf5d9a
2025-07-18 17:31:03 | INFO     | app.api.v1.auth.token:test_swagger_auth:401 - Swagger UI authentication test successful for user: heygo
2025-07-18 17:32:43 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 17:32:43 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 17:32:43 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 17:32:43 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 17:32:43 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 17:32:43 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 17:32:45 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:34:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:34:05 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:34:05 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 17:34:05 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 17:34:05 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 17:34:05 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 17:34:05 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 17:34:05 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 17:37:43 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 17:37:43 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 17:37:43 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 17:37:43 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 17:37:43 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 17:37:43 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 17:37:45 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:37:45 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:37:45 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 17:37:45 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 17:37:45 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 17:37:45 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 17:37:45 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 17:37:45 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 17:40:05 | INFO     | app.api.v1.auth.token:test_swagger_auth:446 - Swagger UI authentication test successful for user: heygo
2025-07-18 17:40:05 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 17:40:05 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 17:40:05 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 17:40:05 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 17:40:05 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 17:40:05 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 17:40:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:40:07 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 17:40:07 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 17:40:07 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 17:40:07 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 17:40:07 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 17:40:07 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 17:40:07 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 17:41:18 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:41:41 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:41:52 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 17:41:52 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: cf25dec3-f944-48e1-b4ff-d0a6e1d913e8
2025-07-18 17:42:02 | INFO     | app.api.v1.auth.token:test_swagger_auth:401 - Swagger UI authentication test successful for user: heygo
2025-07-18 20:15:26 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 20:15:26 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: 6db356b2-9b2b-44b0-815d-84a289d187c8
2025-07-18 20:15:36 | INFO     | app.api.v1.auth.token:refresh_access_token:56 - New access token generated for Swagger user: heygo, jti: e11321d1-f529-43c2-b11c-9d8c5d6e5317
2025-07-18 20:19:25 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 20:20:23 | INFO     | app.api.v1.auth.token:get_access_token:158 - Access token extracted from Authorization header, length: 228
2025-07-18 20:20:23 | INFO     | app.api.v1.auth.token:get_access_token:183 - Access token invalid: 401: Token has expired, trying to refresh...
2025-07-18 20:20:56 | INFO     | app.api.v1.auth.token:get_access_token:158 - Access token extracted from Authorization header, length: 228
2025-07-18 20:20:56 | INFO     | app.api.v1.auth.token:get_access_token:183 - Access token invalid: 401: Token has expired, trying to refresh...
2025-07-18 20:22:06 | INFO     | app.api.v1.auth.token:get_access_token:158 - Access token extracted from Authorization header, length: 228
2025-07-18 20:22:06 | INFO     | app.api.v1.auth.token:get_access_token:183 - Access token invalid: 401: Token has expired, trying to refresh...
2025-07-18 20:22:29 | INFO     | app.api.v1.auth.token:get_access_token:186 - No access token found in Authorization header, trying cookie...
2025-07-18 20:22:29 | INFO     | app.api.v1.auth.token:get_access_token:209 - New access token generated for user: heygo, jti: d87c5b0e-7b5b-4751-9814-a5d5967f610e
2025-07-18 20:22:46 | INFO     | app.api.v1.auth.token:get_access_token:158 - Access token extracted from Authorization header, length: 228
2025-07-18 20:24:03 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 20:24:03 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 20:24:03 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 20:24:03 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 20:24:03 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 20:24:03 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 20:25:27 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:25:29 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:25:29 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:25:29 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 20:25:29 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 20:25:29 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 20:25:29 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 20:25:29 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 20:25:29 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 20:25:34 | INFO     | app.api.v1.auth.token:get_access_token:158 - Access token extracted from Authorization header, length: 228
2025-07-18 20:30:26 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 20:30:26 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 20:30:26 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 20:30:26 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 20:30:26 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 20:30:26 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 20:30:28 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:30:28 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:30:28 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 20:30:28 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 20:30:28 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 20:30:28 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 20:30:28 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 20:30:28 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 20:31:59 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 20:31:59 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 20:31:59 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 20:31:59 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 20:31:59 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 20:31:59 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 20:32:01 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:32:02 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:32:02 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:32:02 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 20:32:02 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 20:32:02 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 20:32:02 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 20:32:02 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 20:32:02 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 20:32:09 | INFO     | app.api.v1.auth.token:get_access_token:159 - Access token extracted from Authorization header, length: 228
2025-07-18 20:33:15 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:33:17 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:33:17 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:33:17 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 20:33:17 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 20:33:17 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 20:33:17 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 20:33:17 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 20:33:17 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 20:33:20 | INFO     | app.api.v1.auth.token:get_access_token:159 - Access token extracted from Authorization header, length: 228
2025-07-18 20:42:46 | INFO     | app.api.v1.auth.token:get_access_token:159 - Access token extracted from Authorization header, length: 228
2025-07-18 20:42:54 | INFO     | app.api.v1.auth.token:get_access_token:159 - Access token extracted from Authorization header, length: 228
2025-07-18 20:43:14 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 20:43:14 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 20:43:14 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 20:43:14 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 20:43:14 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 20:43:14 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 20:43:15 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:43:16 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:43:16 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 20:43:16 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 20:43:16 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 20:43:16 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 20:43:16 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 20:43:16 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 20:43:46 | INFO     | app.api.v1.auth.token:validate_refresh_token:240 - 开始验证 refresh token，token 长度: 229
2025-07-18 20:43:46 | SUCCESS  | app.api.v1.auth.token:validate_refresh_token:254 - Token 验证成功: username=heygo, jti=992dde8e-ad33-48da-bd5b-5b057b7631d7
2025-07-18 20:45:38 | INFO     | app.api.v1.auth.token:get_access_token:159 - Access token extracted from Authorization header, length: 228
2025-07-18 20:45:38 | INFO     | app.api.v1.auth.token:get_access_token:184 - Access token invalid: 401: Token has expired, trying to refresh...
2025-07-18 20:45:44 | INFO     | app.api.v1.auth.token:get_access_token:159 - Access token extracted from Authorization header, length: 228
2025-07-18 20:45:44 | INFO     | app.api.v1.auth.token:get_access_token:184 - Access token invalid: 401: Token has expired, trying to refresh...
2025-07-18 20:45:55 | INFO     | app.api.v1.auth.token:get_access_token:159 - Access token extracted from Authorization header, length: 228
2025-07-18 20:45:55 | INFO     | app.api.v1.auth.token:get_access_token:184 - Access token invalid: 401: Token has expired, trying to refresh...
2025-07-18 20:45:55 | INFO     | app.api.v1.auth.token:get_access_token:208 - New access token generated for user: heygo, jti: 4de52573-7955-4947-bf30-f00738e0ab7c
2025-07-18 20:46:31 | INFO     | app.api.v1.auth.token:get_access_token:159 - Access token extracted from Authorization header, length: 228
2025-07-18 20:48:14 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 20:48:14 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 20:48:14 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 20:48:14 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 20:48:14 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 20:48:14 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 20:48:16 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:48:16 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:48:16 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 20:48:16 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 20:48:16 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 20:48:16 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 20:48:16 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 20:48:16 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 20:48:42 | INFO     | app.api.v1.auth.token:get_access_token:159 - Access token extracted from Authorization header, length: 228
2025-07-18 20:48:42 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 20:48:42 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 20:48:42 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 20:48:42 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 20:48:42 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 20:48:42 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 20:48:44 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:48:44 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:48:44 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 20:48:44 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 20:48:44 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 20:48:44 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 20:48:44 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 20:48:44 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 20:48:55 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 20:48:55 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 20:48:55 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 20:48:55 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 20:48:55 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 20:48:55 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 20:48:57 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:48:58 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:48:58 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:48:58 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 20:48:58 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 20:48:58 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 20:48:58 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 20:48:58 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 20:48:58 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 20:49:02 | WARNING  | app.api.v1.auth.token:get_access_token:161 - Authorization header format invalid: Bearer...
2025-07-18 20:49:02 | INFO     | app.api.v1.auth.token:get_access_token:205 - New access token generated for user: heygo, jti: b70d31a7-a229-4866-a57c-97e2ec7148e2
2025-07-18 20:49:11 | INFO     | app.api.v1.auth.token:get_access_token:159 - Access token extracted from Authorization header, length: 228
2025-07-18 20:49:16 | INFO     | app.api.v1.auth.token:get_access_token:159 - Access token extracted from Authorization header, length: 228
2025-07-18 20:50:22 | INFO     | app.api.v1.auth.token:get_access_token:159 - Access token extracted from Authorization header, length: 228
2025-07-18 20:50:22 | INFO     | app.api.v1.auth.token:get_access_token:183 - Access token invalid: 401: Token has expired, trying to refresh...
2025-07-18 20:50:22 | INFO     | app.api.v1.auth.token:get_access_token:205 - New access token generated for user: heygo, jti: 39cfb5d3-35fa-4573-ab52-6661ded468aa
2025-07-18 20:50:27 | INFO     | app.api.v1.auth.token:get_access_token:159 - Access token extracted from Authorization header, length: 228
2025-07-18 20:50:27 | INFO     | app.api.v1.auth.token:get_access_token:183 - Access token invalid: 401: Token has expired, trying to refresh...
2025-07-18 20:50:27 | INFO     | app.api.v1.auth.token:get_access_token:205 - New access token generated for user: heygo, jti: 0cd83bcc-cdf7-42f3-8efa-8a5de64b5ea7
2025-07-18 20:53:56 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 20:53:56 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 20:53:56 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 20:53:56 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 20:53:56 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 20:53:56 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 20:53:58 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:53:58 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:53:58 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 20:53:58 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 20:53:58 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 20:53:58 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 20:53:58 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 20:53:58 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 20:58:56 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 20:58:56 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 20:58:56 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 20:58:56 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 20:58:56 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 20:58:56 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 20:58:58 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:58:58 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 20:58:58 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 20:58:58 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 20:58:58 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 20:58:58 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 20:58:58 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 20:58:58 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 21:03:56 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 21:03:56 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 21:03:56 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 21:03:56 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 21:03:56 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 21:03:56 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 21:03:58 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 21:03:58 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 21:03:58 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 21:03:58 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 21:03:58 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 21:03:58 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 21:03:58 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 21:03:58 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 21:08:57 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 21:08:57 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 21:08:57 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 21:08:57 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 21:08:57 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 21:08:57 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 21:09:42 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 21:09:42 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 21:09:43 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 21:09:43 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 21:09:43 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 21:09:43 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 21:09:43 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 21:09:43 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-18 21:14:41 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-18 21:14:41 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-18 21:14:41 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-18 21:14:41 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-18 21:14:41 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-18 21:14:41 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-18 21:14:43 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 21:14:43 | INFO     | app.core.utils:setup_logging:77 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-18 21:14:43 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-18 21:14:43 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-18 21:14:43 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-18 21:14:43 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-18 21:14:43 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-18 21:14:43 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-22 18:04:33 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-22 18:04:34 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-22 18:04:34 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-22 18:04:34 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-22 18:04:34 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-22 18:04:34 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-22 18:04:34 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-22 18:04:36 | ERROR    | app.db.redis:init_redis:40 - Redis connection/timeout error: Error 10061 connecting to 127.0.0.1:16379. Connect call failed ('127.0.0.1', 16379).
2025-07-22 18:04:36 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-22 18:06:54 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-22 18:06:54 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-22 18:06:54 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-22 18:06:54 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-22 18:06:54 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-22 18:06:56 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-22 18:06:58 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-22 18:06:58 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-22 18:06:58 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-22 18:06:58 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-22 18:06:58 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-22 18:06:58 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-22 18:06:58 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-22 18:06:58 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-22 18:07:40 | INFO     | app.core.auth:verify_user:125 - 验证用户: heygo
2025-07-22 18:07:40 | INFO     | app.core.auth:verify_user:127 - 获取用户: User(id=1, username=heygo)
2025-07-22 18:07:40 | INFO     | app.core.auth:verify_password:93 - plain: Heytime01!, hashed: $2b$12$JZZD/JyiK2tXBfLMxiFKq.oGoDzncNiSNPNR9QQXPjTzJxbhcbriG
2025-07-22 18:07:40 | INFO     | app.repositories.token_repository:store_refresh_token:72 - Refresh token stored - key: refresh_token:026d3a00-5e74-4dee-9c1b-3982d5a3b9da, sub: heygo, expires in 1295999.012595892 seconds
2025-07-22 18:09:49 | INFO     | app.api.v1.auth.token:get_access_token:187 - No access token found in Authorization header, trying cookie...
2025-07-22 18:09:49 | INFO     | app.api.v1.auth.token:get_access_token:210 - New access token generated for user: heygo, jti: ed082e3f-614f-471f-b639-be2386133d1e
2025-07-22 18:10:00 | INFO     | app.repositories.token_repository:list_all_tokens:197 - Found 2 active refresh tokens
2025-07-22 18:10:28 | INFO     | app.repositories.token_repository:revoke_refresh_token:134 - Refresh token revoked for user: heygo
2025-07-22 18:10:28 | INFO     | app.repositories.token_repository:store_refresh_token:72 - Refresh token stored - key: refresh_token:31492d93-ee12-4725-919d-d6648d2b5318, sub: heygo, expires in 1295999.4756250381 seconds
2025-07-22 18:10:29 | INFO     | app.repositories.token_repository:revoke_refresh_token:134 - Refresh token revoked for user: heygo
2025-07-22 18:10:29 | INFO     | app.repositories.token_repository:store_refresh_token:72 - Refresh token stored - key: refresh_token:307428b8-1db4-4a64-8e1b-08d49f173ede, sub: heygo, expires in 1295999.6147830486 seconds
2025-07-22 18:10:32 | INFO     | app.repositories.token_repository:list_all_tokens:197 - Found 2 active refresh tokens
2025-07-22 18:11:19 | INFO     | app.core.auth:verify_user:125 - 验证用户: heygo
2025-07-22 18:11:19 | INFO     | app.core.auth:verify_user:127 - 获取用户: User(id=1, username=heygo)
2025-07-22 18:11:19 | INFO     | app.core.auth:verify_password:93 - plain: Heytime01!, hashed: $2b$12$JZZD/JyiK2tXBfLMxiFKq.oGoDzncNiSNPNR9QQXPjTzJxbhcbriG
2025-07-22 18:11:19 | INFO     | app.repositories.token_repository:store_refresh_token:72 - Refresh token stored - key: refresh_token:462cf36a-bd2d-4010-b7c6-a21f3b1fb98f, sub: heygo, expires in 1295999.4304790497 seconds
2025-07-22 18:11:40 | INFO     | app.core.auth:get_current_user:391 - Access token refreshed for user: heygo, new JTI: ffd913ea-49ad-49bc-993e-21960d76f8c3
2025-07-22 18:11:40 | WARNING  | app.core.auth:get_user_permission:515 - 角色 user 没有关联的权限
2025-07-22 18:11:40 | INFO     | app.core.auth:get_user_permission:520 - 用户 heygo 的权限: []
2025-07-22 18:13:55 | INFO     | app.core.auth:get_current_user:391 - Access token refreshed for user: heygo, new JTI: 33840af2-c711-4c2a-aba9-dc4cdac280eb
2025-07-22 18:13:55 | WARNING  | app.core.auth:get_user_permission:515 - 角色 user 没有关联的权限
2025-07-22 18:13:55 | INFO     | app.core.auth:get_user_permission:520 - 用户 heygo 的权限: []
2025-07-23 01:50:04 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 01:50:04 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 01:50:04 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 01:50:04 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 01:50:04 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 01:50:04 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 01:50:06 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 01:50:06 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 01:50:06 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 01:50:06 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 01:50:06 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 01:50:06 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 01:50:06 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 01:50:06 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 17:56:47 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 17:56:47 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 17:56:47 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 17:56:47 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 17:56:47 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 17:56:47 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 22:23:37 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:23:39 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:23:39 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:23:39 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 22:23:39 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 22:23:39 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 22:23:39 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 22:23:39 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 22:23:39 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 22:24:18 | INFO     | app.api.v1.auth.token:get_access_token:182 - No access token found in Authorization header, trying cookie...
2025-07-23 22:24:18 | INFO     | app.api.v1.auth.token:get_access_token:205 - New access token generated for user: heygo, jti: None
2025-07-23 22:24:19 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 22:24:19 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 22:24:19 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 22:24:19 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 22:24:19 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 22:24:19 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 22:24:20 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:24:21 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:24:21 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 22:24:21 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 22:24:21 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 22:24:21 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 22:24:21 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 22:24:21 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 22:24:27 | INFO     | app.api.v1.auth.token:get_access_token:182 - No access token found in Authorization header, trying cookie...
2025-07-23 22:24:27 | INFO     | app.api.v1.auth.token:get_access_token:205 - New access token generated for user: heygo, jti: None
2025-07-23 22:24:43 | INFO     | app.api.v1.auth.token:get_access_token:182 - No access token found in Authorization header, trying cookie...
2025-07-23 22:24:43 | INFO     | app.api.v1.auth.token:get_access_token:205 - New access token generated for user: heygo, jti: None
2025-07-23 22:24:46 | INFO     | app.api.v1.auth.token:get_access_token:182 - No access token found in Authorization header, trying cookie...
2025-07-23 22:24:46 | INFO     | app.api.v1.auth.token:get_access_token:205 - New access token generated for user: heygo, jti: None
2025-07-23 22:24:54 | INFO     | app.api.v1.auth.token:get_access_token:182 - No access token found in Authorization header, trying cookie...
2025-07-23 22:24:54 | INFO     | app.api.v1.auth.token:get_access_token:205 - New access token generated for user: heygo, jti: None
2025-07-23 22:31:46 | INFO     | app.api.v1.auth.token:get_access_token:182 - No access token found in Authorization header, trying cookie...
2025-07-23 22:31:46 | INFO     | app.api.v1.auth.token:get_access_token:205 - New access token generated for user: heygo, jti: None
2025-07-23 22:34:19 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 22:34:19 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 22:34:19 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 22:34:19 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 22:34:19 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 22:34:19 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 22:34:21 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:34:21 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:34:21 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 22:34:21 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 22:34:21 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 22:34:21 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 22:34:21 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 22:34:21 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 22:34:39 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 22:34:39 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 22:34:39 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 22:34:39 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 22:34:39 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 22:34:39 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 22:34:41 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:34:41 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:34:41 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 22:34:41 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 22:34:41 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 22:34:41 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 22:34:41 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 22:34:41 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 22:36:26 | INFO     | app.api.v1.auth.token:get_access_token:188 - No access token found in Authorization header, trying cookie...
2025-07-23 22:36:26 | INFO     | app.api.v1.auth.token:get_access_token:211 - New access token generated for user: heygo, jti: None
2025-07-23 22:36:39 | INFO     | app.api.v1.auth.token:get_access_token:188 - No access token found in Authorization header, trying cookie...
2025-07-23 22:36:39 | INFO     | app.api.v1.auth.token:get_access_token:211 - New access token generated for user: heygo, jti: None
2025-07-23 22:39:40 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 22:39:40 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 22:39:40 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 22:39:40 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 22:39:40 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 22:39:40 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 22:41:39 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:41:39 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:41:39 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 22:41:39 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 22:41:39 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 22:41:39 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 22:41:39 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 22:41:39 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 22:48:33 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:48:34 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:48:34 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:48:34 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 22:48:34 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 22:48:34 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 22:48:34 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 22:48:34 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 22:48:34 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 22:53:31 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 22:53:31 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 22:53:31 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 22:53:31 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 22:53:31 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 22:53:31 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 22:53:33 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:53:33 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:53:33 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 22:53:33 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 22:53:33 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 22:53:33 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 22:53:33 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 22:53:33 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 22:57:50 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 21
2025-07-23 22:57:50 | INFO     | app.api.v1.auth.token:get_access_token:179 - Access token invalid: 401: Invalid token, trying to refresh...
2025-07-23 22:57:50 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 22:57:50 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 22:57:50 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 22:57:50 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 22:57:50 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 22:57:50 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 22:57:52 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:57:52 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 22:57:52 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 22:57:52 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 22:57:52 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 22:57:52 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 22:57:52 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 22:57:52 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 22:57:59 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 21
2025-07-23 22:57:59 | INFO     | app.api.v1.auth.token:get_access_token:181 - Access token invalid: 401: Invalid token, trying to refresh...
2025-07-23 23:02:11 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 21
2025-07-23 23:02:11 | INFO     | app.api.v1.auth.token:get_access_token:181 - Access token invalid: 401: Invalid token, trying to refresh...
2025-07-23 23:02:50 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 23:02:50 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 23:02:50 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 23:02:50 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 23:02:50 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 23:02:50 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 23:02:54 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 23:02:54 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 23:02:54 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 23:02:54 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 23:02:54 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 23:02:54 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 23:02:54 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 23:02:54 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 23:03:50 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 21
2025-07-23 23:03:50 | INFO     | app.api.v1.auth.token:get_access_token:181 - Access token invalid: 401: Invalid token, trying to refresh...
2025-07-23 23:03:50 | INFO     | app.api.v1.auth.token:get_access_token:189 - No valid access token found, trying refresh token from cookie...
2025-07-23 23:03:50 | WARNING  | app.api.v1.auth.token:get_access_token:195 - No refresh token found in cookie
2025-07-23 23:03:50 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 23:03:50 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 23:03:50 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 23:03:50 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 23:03:50 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 23:03:50 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 23:03:55 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 23:03:55 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 23:03:55 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 23:03:55 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 23:03:55 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 23:03:55 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 23:03:55 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 23:03:55 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 23:03:56 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 21
2025-07-23 23:03:56 | INFO     | app.api.v1.auth.token:get_access_token:181 - Access token invalid: 401: Invalid token, trying to refresh...
2025-07-23 23:03:56 | INFO     | app.api.v1.auth.token:get_access_token:189 - No valid access token found, trying refresh token from cookie...
2025-07-23 23:03:56 | WARNING  | app.api.v1.auth.token:get_access_token:197 - No refresh token found in cookie
2025-07-23 23:08:54 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 23:08:54 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 23:08:54 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 23:08:54 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 23:08:54 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 23:08:54 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 23:08:56 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 23:08:56 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 23:08:56 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 23:08:56 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 23:08:56 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 23:08:56 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 23:08:56 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 23:08:56 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 23:09:31 | INFO     | app.api.v1.auth.token:validate_refresh_token:252 - 开始验证 refresh token，token 长度: 229
2025-07-23 23:09:31 | ERROR    | app.api.v1.auth.token:validate_refresh_token:285 - 验证 refresh token 失败 (Exception): 'username'
2025-07-23 23:10:22 | INFO     | app.api.v1.auth.token:validate_refresh_token:252 - 开始验证 refresh token，token 长度: 229
2025-07-23 23:10:22 | ERROR    | app.api.v1.auth.token:validate_refresh_token:285 - 验证 refresh token 失败 (Exception): 'username'
2025-07-23 23:10:28 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 23:10:28 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 23:10:28 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 23:10:28 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 23:10:28 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 23:10:28 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 23:10:30 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 23:10:30 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 23:10:30 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 23:10:30 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 23:10:30 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 23:10:30 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 23:10:30 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 23:10:30 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 23:10:42 | INFO     | app.api.v1.auth.token:validate_refresh_token:252 - 开始验证 refresh token，token 长度: 229
2025-07-23 23:10:42 | SUCCESS  | app.api.v1.auth.token:validate_refresh_token:266 - Token 验证成功: username=heygo, jti=462cf36a-bd2d-4010-b7c6-a21f3b1fb98f
2025-07-23 23:11:04 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 21
2025-07-23 23:11:04 | INFO     | app.api.v1.auth.token:get_access_token:181 - Access token invalid: 401: Invalid token, trying to refresh...
2025-07-23 23:11:04 | INFO     | app.api.v1.auth.token:get_access_token:189 - No valid access token found, trying refresh token from cookie...
2025-07-23 23:11:04 | WARNING  | app.api.v1.auth.token:get_access_token:197 - No refresh token found in cookie
2025-07-23 23:11:25 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 21
2025-07-23 23:11:25 | INFO     | app.api.v1.auth.token:get_access_token:181 - Access token invalid: 401: Invalid token, trying to refresh...
2025-07-23 23:11:25 | INFO     | app.api.v1.auth.token:get_access_token:189 - No valid access token found, trying refresh token from cookie...
2025-07-23 23:11:25 | INFO     | app.api.v1.auth.token:get_access_token:204 - Found refresh token, attempting to generate new access token...
2025-07-23 23:11:25 | INFO     | app.api.v1.auth.token:get_access_token:220 - New access token generated for user: heygo, jti: None, expires in 59.51845407485962 seconds
2025-07-23 23:11:43 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 238
2025-07-23 23:11:43 | INFO     | app.api.v1.auth.token:get_access_token:181 - Access token invalid: 401: Invalid token, trying to refresh...
2025-07-23 23:11:43 | INFO     | app.api.v1.auth.token:get_access_token:189 - No valid access token found, trying refresh token from cookie...
2025-07-23 23:11:43 | INFO     | app.api.v1.auth.token:get_access_token:204 - Found refresh token, attempting to generate new access token...
2025-07-23 23:11:43 | INFO     | app.api.v1.auth.token:get_access_token:220 - New access token generated for user: heygo, jti: None, expires in 59.611193895339966 seconds
2025-07-23 23:11:59 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:02 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:04 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:06 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:08 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:12 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:22 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:25 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:27 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:29 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:31 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:33 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:35 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:39 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:40 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:41 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:43 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:43 | INFO     | app.api.v1.auth.token:get_access_token:204 - Found refresh token, attempting to generate new access token...
2025-07-23 23:12:43 | INFO     | app.api.v1.auth.token:get_access_token:220 - New access token generated for user: heygo, jti: None, expires in 59.31605100631714 seconds
2025-07-23 23:12:46 | INFO     | app.api.v1.auth.token:get_access_token:156 - Access token extracted from Authorization header, length: 235
2025-07-23 23:12:46 | INFO     | app.api.v1.auth.token:get_access_token:181 - Access token invalid: 401: Token has expired, trying to refresh...
2025-07-23 23:12:46 | INFO     | app.api.v1.auth.token:get_access_token:189 - No valid access token found, trying refresh token from cookie...
2025-07-23 23:12:46 | INFO     | app.api.v1.auth.token:get_access_token:204 - Found refresh token, attempting to generate new access token...
2025-07-23 23:12:46 | INFO     | app.api.v1.auth.token:get_access_token:220 - New access token generated for user: heygo, jti: None, expires in 59.70517897605896 seconds
2025-07-23 23:20:29 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 23:20:29 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 23:20:29 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 23:20:29 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 23:20:29 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 23:20:29 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 23:20:34 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 23:20:34 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 23:20:34 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 23:20:34 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 23:20:34 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 23:20:34 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 23:20:34 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 23:20:34 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-23 23:25:32 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-23 23:25:32 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-23 23:25:32 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-23 23:25:32 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-23 23:25:32 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-23 23:25:32 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-23 23:25:34 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 23:25:34 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-23 23:25:34 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-23 23:25:34 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: sqlite+aiosqlite:///D:\Projects_Code\Projects\FastAPI_Auth/data/db.sqlite3
2025-07-23 23:25:34 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-23 23:25:34 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-23 23:25:34 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-23 23:25:34 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-31 15:02:28 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 15:02:30 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 15:02:30 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 15:02:30 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 15:02:30 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01@localhost:25432/heygo
2025-07-31 15:02:30 | ERROR    | app.db.init_db:init_db:35 - 创建数据库表时出错: password authentication failed for user "heygo"
2025-07-31 15:03:09 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 15:03:11 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 15:03:11 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 15:03:11 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 15:03:11 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01@localhost:25432/heygo
2025-07-31 15:03:11 | ERROR    | app.db.init_db:init_db:35 - 创建数据库表时出错: password authentication failed for user "heygo"
2025-07-31 15:25:03 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 15:25:03 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 15:25:03 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 15:25:03 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 15:25:03 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 15:25:03 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 15:25:03 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 15:25:03 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-31 15:26:14 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-31 15:26:14 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-31 15:26:14 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 15:26:14 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-31 15:26:14 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-31 15:26:14 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-31 15:27:18 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 15:27:20 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 15:27:20 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 15:27:20 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 15:27:20 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 15:27:20 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 15:27:20 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 15:27:20 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 15:27:20 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-31 17:05:38 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-31 17:05:38 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-31 17:05:38 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 17:05:38 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-31 17:05:38 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-31 17:05:38 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-31 17:05:40 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:05:40 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:05:40 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 17:05:40 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 17:05:41 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 17:05:41 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 17:05:41 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 17:05:41 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-31 17:10:39 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-31 17:10:39 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-31 17:10:39 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 17:10:39 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-31 17:10:39 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-31 17:10:39 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-31 17:10:41 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:10:41 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:10:41 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 17:10:41 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 17:10:41 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 17:10:41 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 17:10:41 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 17:10:41 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-31 17:15:40 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-31 17:15:40 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-31 17:15:40 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 17:15:40 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-31 17:15:40 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-31 17:15:40 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-31 17:15:42 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:15:42 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:15:42 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 17:15:42 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 17:15:42 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 17:15:42 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 17:15:42 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 17:15:42 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-31 17:20:40 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-31 17:20:40 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-31 17:20:40 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 17:20:40 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-31 17:20:40 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-31 17:20:40 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-31 17:20:44 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:20:44 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:20:44 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 17:20:44 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 17:20:44 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 17:20:44 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 17:20:44 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 17:20:44 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-31 17:25:12 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:25:15 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:25:15 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:25:15 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 17:25:15 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 17:25:15 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 17:25:15 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 17:25:15 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 17:25:15 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-31 17:30:11 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-31 17:30:11 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-31 17:30:11 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 17:30:11 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-31 17:30:11 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-31 17:30:11 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-31 17:30:13 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:30:13 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:30:13 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 17:30:13 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 17:30:13 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 17:30:13 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 17:30:13 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 17:30:13 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-31 17:31:44 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-31 17:31:44 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-31 17:31:44 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 17:31:44 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-31 17:31:44 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-31 17:31:44 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-31 17:31:48 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:31:50 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:31:50 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:31:50 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 17:31:50 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 17:31:50 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 17:31:50 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 17:31:50 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 17:31:50 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-31 17:36:22 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-31 17:36:22 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-31 17:36:22 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 17:36:22 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-31 17:36:22 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-31 17:36:22 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-31 17:36:27 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:36:29 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:36:30 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 17:36:30 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 17:36:30 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 17:36:30 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 17:36:30 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 17:36:30 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 17:36:30 | SUCCESS  | app.main:lifespan:30 - 抖音推送下载app_env启动成功
2025-07-31 17:46:25 | INFO     | app.main:lifespan:34 - 正在关闭数据库引擎...
2025-07-31 17:46:25 | INFO     | app.main:lifespan:36 - 数据库引擎已关闭
2025-07-31 17:46:25 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 17:46:25 | INFO     | app.main:lifespan:44 - 正在关闭抖音分析浏览器...
2025-07-31 17:46:25 | INFO     | app.main:lifespan:46 - 抖音分析浏览器已关闭
2025-07-31 17:46:25 | INFO     | app.main:lifespan:50 - 抖音推送下载app_env关闭成功
2025-07-31 18:18:05 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:18:05 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:18:05 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 18:18:05 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 18:18:05 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 18:18:05 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 18:18:05 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 18:18:05 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 18:18:05 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 18:18:05 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 18:18:05 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 18:18:05 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 18:18:05 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 18, updated 0, deactivated 0
2025-07-31 18:18:05 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 18:18:05 | WARNING  | app.core.init_data:init_admin:216 - ADMIN role does not exist. Initializing roles and permissions...
2025-07-31 18:18:05 | SUCCESS  | app.core.init_data:init_roles:71 - Role initialization completed - Created: 3, Updated: 0, Deleted: 0, Skipped: 0
2025-07-31 18:18:05 | SUCCESS  | app.core.init_data:init_permissions:136 - Permission initialization completed - Created: 15, Updated: 0, Deleted: 0, Skipped: 0
2025-07-31 18:18:05 | SUCCESS  | app.core.init_data:init_admin_role_permission:181 - Admin role permissions initialized - Added: 15, Skipped: 0
2025-07-31 18:18:05 | ERROR    | app.core.init_data:init_admin:255 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:18:05 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:18:05 | ERROR    | app.main:lifespan:41 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:18:05 | SUCCESS  | app.main:lifespan:47 - 抖音推送下载app_env started successfully
2025-07-31 18:23:03 | INFO     | app.main:lifespan:53 - 正在关闭数据库引擎...
2025-07-31 18:23:03 | INFO     | app.main:lifespan:55 - 数据库引擎已关闭
2025-07-31 18:23:03 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 18:23:03 | INFO     | app.main:lifespan:63 - 正在关闭抖音分析浏览器...
2025-07-31 18:23:03 | INFO     | app.main:lifespan:65 - 抖音分析浏览器已关闭
2025-07-31 18:23:03 | INFO     | app.main:lifespan:69 - 抖音推送下载app_env关闭成功
2025-07-31 18:23:06 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:23:06 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:23:06 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 18:23:06 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 18:23:06 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 18:23:06 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 18:23:06 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 18:23:06 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 18:23:06 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 18:23:06 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 18:23:06 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 18:23:06 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'api_permissions_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'api_permissions_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'user_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'user_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'api_permissions_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'permission_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'api_permissions_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'role_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'user_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'token_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'token_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'api_permissions_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'public_apis'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'public_apis'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'permission_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'role_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'permission_management'}}
2025-07-31 18:23:06 | SUCCESS  | app.repositories.user_repository:update_api_permission:216 - 更新的字段: {'category': {'old': 'default', 'new': 'public_apis'}}
2025-07-31 18:23:06 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 18:23:06 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 18:23:07 | ERROR    | app.core.init_data:init_admin:255 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:23:07 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:23:07 | ERROR    | app.main:lifespan:41 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:23:07 | SUCCESS  | app.main:lifespan:46 - 抖音推送下载app_env started successfully
2025-07-31 18:28:04 | INFO     | app.main:lifespan:52 - 正在关闭数据库引擎...
2025-07-31 18:28:04 | INFO     | app.main:lifespan:54 - 数据库引擎已关闭
2025-07-31 18:28:04 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 18:28:04 | INFO     | app.main:lifespan:62 - 正在关闭抖音分析浏览器...
2025-07-31 18:28:04 | INFO     | app.main:lifespan:64 - 抖音分析浏览器已关闭
2025-07-31 18:28:04 | INFO     | app.main:lifespan:68 - 抖音推送下载app_env关闭成功
2025-07-31 18:28:36 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:28:36 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:28:36 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 18:28:36 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 18:28:36 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 18:28:36 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 18:28:36 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 18:28:36 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 18:28:36 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 18:28:36 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 18:28:36 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 18:28:36 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:28:36 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 18:28:36 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 18:28:36 | ERROR    | app.core.init_data:init_admin:252 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:28:36 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:28:36 | ERROR    | app.main:lifespan:44 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:28:36 | SUCCESS  | app.main:lifespan:49 - 抖音推送下载app_env started successfully
2025-07-31 18:29:43 | INFO     | app.main:lifespan:55 - 正在关闭数据库引擎...
2025-07-31 18:29:43 | INFO     | app.main:lifespan:58 - 数据库引擎已关闭
2025-07-31 18:29:43 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 18:29:43 | INFO     | app.main:lifespan:66 - 正在关闭抖音分析浏览器...
2025-07-31 18:29:43 | INFO     | app.main:lifespan:68 - 抖音分析浏览器已关闭
2025-07-31 18:29:43 | INFO     | app.main:lifespan:72 - 抖音推送下载app_env关闭成功
2025-07-31 18:29:46 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:29:46 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:29:46 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 18:29:46 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 18:29:46 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 18:29:46 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 18:29:46 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 18:29:46 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 18:29:46 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 18:29:46 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 18:29:46 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 18:29:46 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:46 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 18:29:46 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 18:29:46 | ERROR    | app.core.init_data:init_admin:252 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:29:46 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:29:46 | ERROR    | app.main:lifespan:44 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:29:46 | SUCCESS  | app.main:lifespan:49 - 抖音推送下载app_env started successfully
2025-07-31 18:29:51 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:29:53 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:29:53 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:29:53 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 18:29:53 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 18:29:53 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 18:29:53 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 18:29:53 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 18:29:53 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 18:29:53 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 18:29:53 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 18:29:53 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 18:29:53 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:29:53 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 18:29:53 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 18:29:54 | ERROR    | app.core.init_data:init_admin:252 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:29:54 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:29:54 | ERROR    | app.main:lifespan:44 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:29:54 | SUCCESS  | app.main:lifespan:49 - 抖音推送下载app_env started successfully
2025-07-31 18:31:47 | INFO     | app.main:lifespan:55 - 正在关闭数据库引擎...
2025-07-31 18:31:47 | INFO     | app.main:lifespan:57 - 数据库引擎已关闭
2025-07-31 18:31:47 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 18:31:47 | INFO     | app.main:lifespan:65 - 正在关闭抖音分析浏览器...
2025-07-31 18:31:47 | INFO     | app.main:lifespan:67 - 抖音分析浏览器已关闭
2025-07-31 18:31:47 | INFO     | app.main:lifespan:71 - 抖音推送下载app_env关闭成功
2025-07-31 18:31:50 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:31:50 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:31:50 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 18:31:50 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 18:31:50 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 18:31:50 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 18:31:50 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 18:31:50 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 18:31:50 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 18:31:50 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 18:31:50 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 18:31:50 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:50 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 18:31:50 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 18:31:51 | ERROR    | app.core.init_data:init_admin:252 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:31:51 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:31:51 | ERROR    | app.main:lifespan:44 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:31:51 | SUCCESS  | app.main:lifespan:49 - 抖音推送下载app_env started successfully
2025-07-31 18:31:57 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:31:59 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:31:59 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:31:59 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 18:31:59 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 18:31:59 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 18:31:59 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 18:31:59 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 18:31:59 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 18:31:59 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 18:31:59 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 18:31:59 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 18:31:59 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:31:59 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 18:31:59 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 18:31:59 | ERROR    | app.core.init_data:init_admin:252 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:31:59 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:31:59 | ERROR    | app.main:lifespan:44 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:31:59 | SUCCESS  | app.main:lifespan:47 - 抖音推送下载app_env started successfully
2025-07-31 18:36:54 | INFO     | app.main:lifespan:53 - 正在关闭数据库引擎...
2025-07-31 18:36:54 | INFO     | app.main:lifespan:55 - 数据库引擎已关闭
2025-07-31 18:36:54 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 18:36:54 | INFO     | app.main:lifespan:63 - 正在关闭抖音分析浏览器...
2025-07-31 18:36:54 | INFO     | app.main:lifespan:65 - 抖音分析浏览器已关闭
2025-07-31 18:36:54 | INFO     | app.main:lifespan:69 - 抖音推送下载app_env关闭成功
2025-07-31 18:36:57 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:36:57 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:36:57 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 18:36:57 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 18:36:57 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 18:36:57 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 18:36:57 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 18:36:57 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 18:36:57 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 18:36:57 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 18:36:57 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 18:36:57 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:36:57 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 18:36:57 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 18:36:57 | ERROR    | app.core.init_data:init_admin:252 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:36:57 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:36:57 | ERROR    | app.main:lifespan:48 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:36:57 | SUCCESS  | app.main:lifespan:51 - 抖音推送下载app_env started successfully
2025-07-31 18:40:40 | INFO     | app.main:lifespan:57 - 正在关闭数据库引擎...
2025-07-31 18:40:40 | INFO     | app.main:lifespan:59 - 数据库引擎已关闭
2025-07-31 18:40:40 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 18:40:40 | INFO     | app.main:lifespan:67 - 正在关闭抖音分析浏览器...
2025-07-31 18:40:40 | INFO     | app.main:lifespan:69 - 抖音分析浏览器已关闭
2025-07-31 18:40:40 | INFO     | app.main:lifespan:73 - 抖音推送下载app_env关闭成功
2025-07-31 18:40:43 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:40:43 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:40:43 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 18:40:43 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 18:40:43 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 18:40:43 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 18:40:43 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 18:40:43 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 18:40:43 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 18:40:43 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 18:40:43 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 18:40:43 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:40:43 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 18:40:43 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 18:40:43 | ERROR    | app.core.init_data:init_admin:252 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:40:43 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:40:43 | ERROR    | app.main:lifespan:48 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:40:43 | SUCCESS  | app.main:lifespan:53 - 抖音推送下载app_env started successfully
2025-07-31 18:41:54 | INFO     | app.main:lifespan:59 - 正在关闭数据库引擎...
2025-07-31 18:41:54 | INFO     | app.main:lifespan:61 - 数据库引擎已关闭
2025-07-31 18:41:54 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 18:41:54 | INFO     | app.main:lifespan:69 - 正在关闭抖音分析浏览器...
2025-07-31 18:41:54 | INFO     | app.main:lifespan:71 - 抖音分析浏览器已关闭
2025-07-31 18:41:54 | INFO     | app.main:lifespan:75 - 抖音推送下载app_env关闭成功
2025-07-31 18:41:56 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:41:56 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:41:56 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 18:41:56 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 18:41:56 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 18:41:56 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 18:41:56 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 18:41:56 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 18:41:57 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 18:41:57 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 18:41:57 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 18:41:57 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:41:57 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 18:41:57 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 18:41:57 | ERROR    | app.core.init_data:init_admin:252 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:41:57 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:41:57 | ERROR    | app.main:lifespan:48 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:41:57 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-07-31 18:43:58 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:44:00 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:44:00 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:44:00 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 18:44:00 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 18:44:00 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 18:44:00 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 18:44:00 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 18:44:00 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 18:44:00 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 18:44:00 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 18:44:00 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 18:44:00 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:44:00 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 18:44:00 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 18:44:00 | ERROR    | app.core.init_data:init_admin:252 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:44:00 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:44:00 | ERROR    | app.main:lifespan:48 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:44:00 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-07-31 18:53:56 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-07-31 18:53:56 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-07-31 18:53:56 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 18:53:56 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-07-31 18:53:56 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-07-31 18:53:56 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-07-31 18:53:59 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:53:59 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:53:59 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 18:53:59 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 18:53:59 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 18:53:59 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 18:53:59 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 18:53:59 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 18:53:59 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 18:53:59 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 18:53:59 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 18:53:59 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:53:59 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 18:53:59 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 18:53:59 | ERROR    | app.core.init_data:init_admin:252 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:53:59 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:53:59 | ERROR    | app.main:lifespan:48 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:53:59 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-07-31 18:56:01 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-07-31 18:56:01 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-07-31 18:56:01 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 18:56:01 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-07-31 18:56:01 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-07-31 18:56:01 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-07-31 18:56:04 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:56:04 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 18:56:04 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 18:56:04 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 18:56:04 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 18:56:04 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 18:56:04 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 18:56:04 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 18:56:05 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 18:56:05 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 18:56:05 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 18:56:05 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 18:56:05 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 18:56:05 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 18:56:05 | ERROR    | app.core.init_data:init_admin:252 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:56:05 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:56:05 | ERROR    | app.main:lifespan:48 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 18:56:05 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-07-31 19:01:02 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-07-31 19:01:02 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-07-31 19:01:02 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 19:01:02 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-07-31 19:01:02 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-07-31 19:01:02 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-07-31 19:01:04 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:01:04 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:01:05 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 19:01:05 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 19:01:05 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 19:01:05 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 19:01:05 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 19:01:05 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 19:01:05 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 19:01:05 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 19:01:05 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 19:01:05 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:01:05 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 19:01:05 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 19:01:05 | ERROR    | app.core.init_data:init_admin:252 - Failed to create admin user: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 19:01:05 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 19:01:05 | ERROR    | app.main:lifespan:48 - Application initialization failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-31 19:01:05 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-07-31 19:06:03 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-07-31 19:06:03 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-07-31 19:06:03 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 19:06:03 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-07-31 19:06:03 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-07-31 19:06:03 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-07-31 19:06:05 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:06:05 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:06:05 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 19:06:05 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 19:06:05 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 19:06:05 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 19:06:05 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 19:06:05 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 19:06:06 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 19:06:06 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 19:06:06 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 19:06:06 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:06:06 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 19:06:06 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 19:06:06 | SUCCESS  | app.core.init_data:init_admin:246 - Admin user created successfully - username: admin, password: admin
2025-07-31 19:06:06 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-07-31 19:06:06 | WARNING  | app.core.startup:initialize_app:63 - 🔐 Default admin credentials created:
2025-07-31 19:06:06 | WARNING  | app.core.startup:initialize_app:64 -    Username: admin
2025-07-31 19:06:06 | WARNING  | app.core.startup:initialize_app:65 -    Password: admin
2025-07-31 19:06:06 | WARNING  | app.core.startup:initialize_app:66 -    ⚠️  Please change the default password after first login!
2025-07-31 19:06:06 | INFO     | app.core.startup:initialize_app:78 - Application initialization completed
2025-07-31 19:06:06 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-07-31 19:06:06 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-07-31 19:11:04 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-07-31 19:11:04 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-07-31 19:11:04 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 19:11:04 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-07-31 19:11:04 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-07-31 19:11:04 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-07-31 19:11:06 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:11:06 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:11:06 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 19:11:06 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 19:11:06 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 19:11:06 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 19:11:06 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 19:11:06 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 19:11:06 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 19:11:06 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 19:11:06 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 19:11:06 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:11:06 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 19:11:06 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 19:11:07 | SUCCESS  | app.core.init_data:init_admin:246 - Admin user created successfully - username: admin, password: admin
2025-07-31 19:11:07 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-07-31 19:11:07 | WARNING  | app.core.startup:initialize_app:63 - 🔐 Default admin credentials created:
2025-07-31 19:11:07 | WARNING  | app.core.startup:initialize_app:64 -    Username: admin
2025-07-31 19:11:07 | WARNING  | app.core.startup:initialize_app:65 -    Password: admin
2025-07-31 19:11:07 | WARNING  | app.core.startup:initialize_app:66 -    ⚠️  Please change the default password after first login!
2025-07-31 19:11:07 | INFO     | app.core.startup:initialize_app:78 - Application initialization completed
2025-07-31 19:11:07 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-07-31 19:11:07 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-07-31 19:16:04 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-07-31 19:16:04 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-07-31 19:16:04 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 19:16:04 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-07-31 19:16:04 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-07-31 19:16:04 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-07-31 19:16:07 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:16:07 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:16:07 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 19:16:07 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 19:16:07 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 19:16:07 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 19:16:07 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 19:16:07 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 19:16:07 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 19:16:07 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 19:16:07 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 19:16:07 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:16:07 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 19:16:07 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 19:16:08 | SUCCESS  | app.core.init_data:init_admin:246 - Admin user created successfully - username: admin, password: admin
2025-07-31 19:16:08 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-07-31 19:16:08 | WARNING  | app.core.startup:initialize_app:63 - 🔐 Default admin credentials created:
2025-07-31 19:16:08 | WARNING  | app.core.startup:initialize_app:64 -    Username: admin
2025-07-31 19:16:08 | WARNING  | app.core.startup:initialize_app:65 -    Password: admin
2025-07-31 19:16:08 | WARNING  | app.core.startup:initialize_app:66 -    ⚠️  Please change the default password after first login!
2025-07-31 19:16:08 | INFO     | app.core.startup:initialize_app:78 - Application initialization completed
2025-07-31 19:16:08 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-07-31 19:16:08 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-07-31 19:17:13 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:17:16 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:17:16 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:17:16 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 19:17:16 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 19:17:16 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 19:17:16 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 19:17:16 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 19:17:16 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 19:17:16 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 19:17:16 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 19:17:16 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 19:17:16 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:16 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 19:17:16 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 19:17:17 | SUCCESS  | app.core.init_data:init_admin:246 - Admin user created successfully - username: admin, password: admin
2025-07-31 19:17:17 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-07-31 19:17:17 | WARNING  | app.core.startup:initialize_app:63 - 🔐 Default admin credentials created:
2025-07-31 19:17:17 | WARNING  | app.core.startup:initialize_app:64 -    Username: admin
2025-07-31 19:17:17 | WARNING  | app.core.startup:initialize_app:65 -    Password: admin
2025-07-31 19:17:17 | WARNING  | app.core.startup:initialize_app:66 -    ⚠️  Please change the default password after first login!
2025-07-31 19:17:17 | INFO     | app.core.startup:initialize_app:78 - Application initialization completed
2025-07-31 19:17:17 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-07-31 19:17:17 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-07-31 19:17:24 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-07-31 19:17:24 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-07-31 19:17:24 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 19:17:24 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-07-31 19:17:24 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-07-31 19:17:24 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-07-31 19:17:27 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:17:30 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:17:30 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:17:30 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 19:17:30 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 19:17:30 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 19:17:30 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 19:17:30 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 19:17:30 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 19:17:30 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 19:17:30 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 19:17:30 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 19:17:30 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.repositories.user_repository:update_api_permission:219 - 没有字段发生实际更新
2025-07-31 19:17:30 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-07-31 19:17:30 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 19:17:30 | INFO     | app.core.init_data:init_admin:208 - Admin user already exists, skipping creation
2025-07-31 19:17:30 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-07-31 19:17:30 | INFO     | app.core.startup:initialize_app:68 - Admin user already exists, skipping creation
2025-07-31 19:17:30 | INFO     | app.core.startup:initialize_app:78 - Application initialization completed
2025-07-31 19:17:30 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-07-31 19:17:30 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-07-31 19:22:25 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-07-31 19:22:25 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-07-31 19:22:25 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 19:22:25 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-07-31 19:22:25 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-07-31 19:22:25 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-07-31 19:22:28 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:22:28 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:22:28 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 19:22:28 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 19:22:28 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 19:22:28 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 19:22:28 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 19:22:28 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 19:22:28 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 19:22:28 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 19:22:28 | WARNING  | app.core.sync_api_permission:validate_config:273 - 配置验证发现18个错误
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:20 - API permissions configuration validation failed:
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第1项 (/auth/remove_roles_from_user) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第2项 (/auth/assign_roles_to_user) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第3项 (/auth/get_user_roles) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第4项 (/auth/create_role) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第5项 (/auth/delete_role) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第6项 (/auth/create_permission) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第7项 (/auth/assign_permission_to_role) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第8项 (/auth/remove_permission_from_role) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第9项 (/auth/create_token_with_scopes) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第10项 (/auth/revoke_token) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第11项 (/auth/login) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第12项 (/auth/register) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第13项 (/auth/get_current_user_permissions) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第14项 (/admin/api_permissions/sync_api_permissions) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第15项 (/admin/api_permissions/validate_api_permissions_yml) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第16项 (/admin/api_permissions/export_db_to_yaml) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第17项 (/admin/api_permissions/get_all_api_permissions) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:22 -   - 权限配置第18项 (/admin/api_permissions/toggle_api_permission) 错误: 缺少必需字段 'method'
2025-07-31 19:22:28 | ERROR    | app.core.startup:sync_permissions_on_startup:33 - Failed to sync API permissions on startup: API permissions configuration validation failed
2025-07-31 19:22:28 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 19:22:28 | INFO     | app.core.init_data:init_admin:208 - Admin user already exists, skipping creation
2025-07-31 19:22:28 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-07-31 19:22:28 | INFO     | app.core.startup:initialize_app:68 - Admin user already exists, skipping creation
2025-07-31 19:22:28 | INFO     | app.core.startup:initialize_app:78 - Application initialization completed
2025-07-31 19:22:28 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-07-31 19:22:28 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-07-31 19:27:25 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-07-31 19:27:25 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-07-31 19:27:25 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 19:27:25 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-07-31 19:27:25 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-07-31 19:27:25 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-07-31 19:27:28 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:27:28 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:27:28 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 19:27:28 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 19:27:28 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 19:27:28 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 19:27:28 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 19:27:28 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 19:27:28 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 19:27:28 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 19:27:28 | INFO     | app.core.sync_api_permission:validate_config:315 - 配置验证通过: 共18个API权限配置
2025-07-31 19:27:28 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 19:27:28 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 19:27:28 | INFO     | app.core.sync_api_permission:sync_yml_to_db:183 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18
2025-07-31 19:27:28 | INFO     | app.core.sync_api_permission:sync_yml_to_db:214 - Checking 18 existing permissions for updates...
2025-07-31 19:27:28 | SUCCESS  | app.core.sync_api_permission:sync_yml_to_db:240 - API permissions sync completed - Created: 0, Updated: 0, Deactivated: 0, Reactivated: 0, Skipped: 18
2025-07-31 19:27:28 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 0, deactivated 0
2025-07-31 19:27:28 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 19:27:28 | INFO     | app.core.init_data:init_admin:208 - Admin user already exists, skipping creation
2025-07-31 19:27:28 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-07-31 19:27:28 | INFO     | app.core.startup:initialize_app:68 - Admin user already exists, skipping creation
2025-07-31 19:27:28 | INFO     | app.core.startup:initialize_app:78 - Application initialization completed
2025-07-31 19:27:28 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-07-31 19:27:28 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-07-31 19:32:26 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-07-31 19:32:26 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-07-31 19:32:26 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 19:32:26 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-07-31 19:32:26 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-07-31 19:32:26 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-07-31 19:32:29 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:32:29 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-07-31 19:32:29 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-07-31 19:32:29 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-07-31 19:32:29 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-07-31 19:32:29 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-07-31 19:32:29 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-07-31 19:32:29 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-07-31 19:32:29 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-07-31 19:32:29 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-07-31 19:32:29 | INFO     | app.core.sync_api_permission:validate_config:315 - 配置验证通过: 共18个API权限配置
2025-07-31 19:32:29 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-07-31 19:32:29 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-07-31 19:32:29 | ERROR    | app.core.sync_api_permission:sync_yml_to_db:247 - API permissions sync failed: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedTableError'>: relation "auth.api_scopes" does not exist
[SQL: SELECT auth.api_scopes.id, auth.api_scopes.api_path, auth.api_scopes.description, auth.api_scopes.required_scopes, auth.api_scopes.is_active, auth.api_scopes.category, auth.api_scopes.created_at, auth.api_scopes.updated_at 
FROM auth.api_scopes 
WHERE auth.api_scopes.is_active]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-31 19:32:29 | ERROR    | app.core.startup:sync_permissions_on_startup:33 - Failed to sync API permissions on startup: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedTableError'>: relation "auth.api_scopes" does not exist
[SQL: SELECT auth.api_scopes.id, auth.api_scopes.api_path, auth.api_scopes.description, auth.api_scopes.required_scopes, auth.api_scopes.is_active, auth.api_scopes.category, auth.api_scopes.created_at, auth.api_scopes.updated_at 
FROM auth.api_scopes 
WHERE auth.api_scopes.is_active]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-31 19:32:29 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-07-31 19:32:29 | ERROR    | app.core.init_data:init_admin:256 - Failed to create admin user: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.InFailedSQLTransactionError'>: current transaction is aborted, commands ignored until end of transaction block
[SQL: SELECT auth.users.id, auth.users.username, auth.users.password, auth.users.created_at, auth.users.updated_at 
FROM auth.users 
WHERE auth.users.username = $1::VARCHAR]
[parameters: ('admin',)]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
2025-07-31 19:32:29 | ERROR    | app.core.startup:initialize_app:81 - Application initialization failed: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.InFailedSQLTransactionError'>: current transaction is aborted, commands ignored until end of transaction block
[SQL: SELECT auth.users.id, auth.users.username, auth.users.password, auth.users.created_at, auth.users.updated_at 
FROM auth.users 
WHERE auth.users.username = $1::VARCHAR]
[parameters: ('admin',)]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
2025-07-31 19:32:29 | ERROR    | app.main:lifespan:48 - Application initialization failed: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.InFailedSQLTransactionError'>: current transaction is aborted, commands ignored until end of transaction block
[SQL: SELECT auth.users.id, auth.users.username, auth.users.password, auth.users.created_at, auth.users.updated_at 
FROM auth.users 
WHERE auth.users.username = $1::VARCHAR]
[parameters: ('admin',)]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
2025-07-31 19:32:29 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-07-31 19:37:27 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-07-31 19:37:27 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-07-31 19:37:27 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-07-31 19:37:27 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-07-31 19:37:27 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-07-31 19:37:27 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-08-01 08:49:51 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 08:49:57 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 08:49:57 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 08:49:57 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 08:49:57 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 08:49:57 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 08:49:57 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 08:49:57 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 08:49:57 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 08:49:57 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 08:49:57 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-08-01 08:49:57 | INFO     | app.core.sync_api_permission:validate_config:315 - 配置验证通过: 共18个API权限配置
2025-08-01 08:49:57 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-08-01 08:49:57 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-08-01 08:49:57 | INFO     | app.core.sync_api_permission:sync_yml_to_db:183 - Sync analysis: create=18, deactivate=0, reactivate=0, potentially_update=0
2025-08-01 08:49:57 | INFO     | app.core.sync_api_permission:sync_yml_to_db:188 - Creating 18 new API permissions...
2025-08-01 08:49:57 | SUCCESS  | app.core.sync_api_permission:sync_yml_to_db:240 - API scopes sync completed - Created: 18, Updated: 0, Deactivated: 0, Reactivated: 0, Skipped: 0
2025-08-01 08:49:57 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 18, updated 0, deactivated 0
2025-08-01 08:49:57 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 08:49:57 | INFO     | app.core.init_data:init_admin:209 - Admin user already exists, skipping creation
2025-08-01 08:49:57 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 08:49:57 | INFO     | app.core.startup:initialize_app:68 - Admin user already exists, skipping creation
2025-08-01 08:49:57 | INFO     | app.core.startup:initialize_app:78 - Application initialization completed
2025-08-01 08:49:57 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 08:49:57 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 08:52:05 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-08-01 08:52:05 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-08-01 08:52:05 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-08-01 08:52:05 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-08-01 08:52:05 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-08-01 08:52:05 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-08-01 08:52:09 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 08:52:12 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 08:52:12 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 08:52:12 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 08:52:12 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 08:52:12 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 08:52:12 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 08:52:12 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 08:52:12 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 08:52:13 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 08:52:13 | INFO     | app.core.startup:sync_permissions_on_startup:16 - Validating API permissions configuration...
2025-08-01 08:52:13 | INFO     | app.core.sync_api_permission:validate_config:315 - 配置验证通过: 共18个API权限配置
2025-08-01 08:52:13 | INFO     | app.core.startup:sync_permissions_on_startup:25 - API permissions configuration validation passed
2025-08-01 08:52:13 | INFO     | app.core.startup:sync_permissions_on_startup:28 - Syncing API permissions to database...
2025-08-01 08:52:13 | INFO     | app.core.sync_api_permission:sync_yml_to_db:183 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18
2025-08-01 08:52:13 | INFO     | app.core.sync_api_permission:sync_yml_to_db:214 - Checking 18 existing permissions for updates...
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'role_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'permission_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'public_apis'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'public_apis'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'role_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'token_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'user_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'user_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'api_permissions_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'api_permissions_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'token_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'permission_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'public_apis'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'api_permissions_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'api_permissions_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'user_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'permission_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.repositories.user_repository:update_api_scope:216 - 更新的字段: {'category': {'old': 'default', 'new': 'api_permissions_management'}}
2025-08-01 08:52:13 | SUCCESS  | app.core.sync_api_permission:sync_yml_to_db:240 - API scopes sync completed - Created: 0, Updated: 18, Deactivated: 0, Reactivated: 0, Skipped: 0
2025-08-01 08:52:13 | INFO     | app.core.startup:sync_permissions_on_startup:30 - API permissions sync completed: created 0, updated 18, deactivated 0
2025-08-01 08:52:13 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 08:52:13 | SUCCESS  | app.core.init_data:init_admin:248 - Admin user created successfully - username: admin, password: admin
2025-08-01 08:52:13 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 08:52:13 | WARNING  | app.core.startup:initialize_app:63 - 🔐 Default admin credentials created:
2025-08-01 08:52:13 | WARNING  | app.core.startup:initialize_app:64 -    Username: admin
2025-08-01 08:52:13 | WARNING  | app.core.startup:initialize_app:65 -    Password: admin
2025-08-01 08:52:13 | WARNING  | app.core.startup:initialize_app:66 -    ⚠️  Please change the default password after first login!
2025-08-01 08:52:13 | INFO     | app.core.startup:initialize_app:78 - Application initialization completed
2025-08-01 08:52:13 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 08:52:13 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 09:58:52 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-08-01 09:58:52 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-08-01 09:58:52 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-08-01 09:58:52 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-08-01 09:58:52 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-08-01 09:58:52 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-08-01 10:04:29 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:04:34 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:04:34 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:04:34 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 10:04:34 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 10:04:34 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 10:04:34 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 10:04:34 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 10:04:34 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 10:04:34 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 10:04:34 | INFO     | app.core.startup:sync_api_scopes_on_startup:16 - Validating API scopes configuration...
2025-08-01 10:04:34 | INFO     | app.core.sync_api_scopes:validate_config:315 - 配置验证通过: 共18个API权限配置
2025-08-01 10:04:34 | INFO     | app.core.startup:sync_api_scopes_on_startup:25 - API scopes configuration validation passed
2025-08-01 10:04:34 | INFO     | app.core.startup:sync_api_scopes_on_startup:28 - Syncing API scopes to database...
2025-08-01 10:04:34 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:183 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18
2025-08-01 10:04:34 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:214 - Checking 18 existing scopes for updates...
2025-08-01 10:04:34 | SUCCESS  | app.core.sync_api_scopes:sync_yml_to_db:240 - API scopes sync completed - Created: 0, Updated: 0, Deactivated: 0, Reactivated: 0, Skipped: 18
2025-08-01 10:04:34 | INFO     | app.core.startup:sync_api_scopes_on_startup:30 - API scopes sync completed: created 0, updated 0, deactivated 0
2025-08-01 10:04:34 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 10:04:34 | INFO     | app.core.init_data:init_admin:209 - Admin user already exists, skipping creation
2025-08-01 10:04:34 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 10:04:34 | INFO     | app.core.startup:initialize_app:71 - Admin user already exists, skipping creation
2025-08-01 10:04:34 | INFO     | app.core.startup:initialize_app:81 - Application initialization completed
2025-08-01 10:04:34 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 10:04:34 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 10:14:27 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-08-01 10:14:27 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-08-01 10:14:27 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-08-01 10:14:27 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-08-01 10:14:27 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-08-01 10:14:27 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-08-01 10:14:32 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:14:33 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:14:33 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 10:14:33 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 10:14:33 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 10:14:33 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 10:14:33 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 10:14:33 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 10:14:33 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 10:14:33 | INFO     | app.core.startup:sync_api_scopes_on_startup:16 - Validating API scopes configuration...
2025-08-01 10:14:33 | INFO     | app.core.sync_api_scopes:validate_config:315 - 配置验证通过: 共18个API权限配置
2025-08-01 10:14:33 | INFO     | app.core.startup:sync_api_scopes_on_startup:25 - API scopes configuration validation passed
2025-08-01 10:14:33 | INFO     | app.core.startup:sync_api_scopes_on_startup:28 - Syncing API scopes to database...
2025-08-01 10:14:33 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:183 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18
2025-08-01 10:14:33 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:214 - Checking 18 existing scopes for updates...
2025-08-01 10:14:33 | SUCCESS  | app.core.sync_api_scopes:sync_yml_to_db:240 - API scopes sync completed - Created: 0, Updated: 0, Deactivated: 0, Reactivated: 0, Skipped: 18
2025-08-01 10:14:33 | INFO     | app.core.startup:sync_api_scopes_on_startup:30 - API scopes sync completed: created 0, updated 0, deactivated 0
2025-08-01 10:14:33 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 10:14:33 | INFO     | app.core.init_data:init_admin:209 - Admin user already exists, skipping creation
2025-08-01 10:14:33 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 10:14:33 | INFO     | app.core.startup:initialize_app:71 - Admin user already exists, skipping creation
2025-08-01 10:14:33 | INFO     | app.core.startup:initialize_app:81 - Application initialization completed
2025-08-01 10:14:33 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 10:14:33 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 10:19:31 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-08-01 10:19:31 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-08-01 10:19:31 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-08-01 10:19:31 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-08-01 10:19:31 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-08-01 10:19:31 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-08-01 10:19:34 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:19:34 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:19:34 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 10:19:34 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 10:19:34 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 10:19:34 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 10:19:34 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 10:19:34 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 10:19:34 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 10:19:34 | INFO     | app.core.startup:sync_api_scopes_on_startup:16 - Validating API scopes configuration...
2025-08-01 10:19:34 | INFO     | app.core.sync_api_scopes:validate_config:315 - Configuration validation passed: 18 API scopes configurations
2025-08-01 10:19:34 | INFO     | app.core.startup:sync_api_scopes_on_startup:25 - API scopes configuration validation passed
2025-08-01 10:19:34 | INFO     | app.core.startup:sync_api_scopes_on_startup:28 - Syncing API scopes to database...
2025-08-01 10:19:34 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:183 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18
2025-08-01 10:19:34 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:214 - Checking 18 existing scopes for updates...
2025-08-01 10:19:34 | SUCCESS  | app.core.sync_api_scopes:sync_yml_to_db:240 - API scopes sync completed - Created: 0, Updated: 0, Deactivated: 0, Reactivated: 0, Skipped: 18
2025-08-01 10:19:34 | INFO     | app.core.startup:sync_api_scopes_on_startup:30 - API scopes sync completed: created 0, updated 0, deactivated 0
2025-08-01 10:19:34 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 10:19:34 | INFO     | app.core.init_data:init_admin:209 - Admin user already exists, skipping creation
2025-08-01 10:19:34 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 10:19:34 | INFO     | app.core.startup:initialize_app:71 - Admin user already exists, skipping creation
2025-08-01 10:19:34 | INFO     | app.core.startup:initialize_app:81 - Application initialization completed
2025-08-01 10:19:34 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 10:19:34 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 10:20:29 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-08-01 10:20:29 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-08-01 10:20:29 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-08-01 10:20:29 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-08-01 10:20:29 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-08-01 10:20:29 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-08-01 10:20:33 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:20:35 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:20:35 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:20:35 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 10:20:35 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 10:20:35 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 10:20:35 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 10:20:35 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 10:20:35 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 10:20:36 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 10:20:36 | INFO     | app.core.startup:sync_api_scopes_on_startup:16 - Validating API scopes configuration...
2025-08-01 10:20:36 | INFO     | app.core.sync_api_scopes:validate_config:315 - Configuration validation passed: 18 API scopes configurations
2025-08-01 10:20:36 | INFO     | app.core.startup:sync_api_scopes_on_startup:25 - API scopes configuration validation passed
2025-08-01 10:20:36 | INFO     | app.core.startup:sync_api_scopes_on_startup:28 - Syncing API scopes to database...
2025-08-01 10:20:36 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:183 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18
2025-08-01 10:20:36 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:214 - Checking 18 existing scopes for updates...
2025-08-01 10:20:36 | SUCCESS  | app.core.sync_api_scopes:sync_yml_to_db:240 - API scopes sync completed - Created: 0, Updated: 0, Deactivated: 0, Reactivated: 0, Skipped: 18
2025-08-01 10:20:36 | INFO     | app.core.startup:sync_api_scopes_on_startup:30 - API scopes sync completed: created 0, updated 0, deactivated 0
2025-08-01 10:20:36 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 10:20:36 | INFO     | app.core.init_data:init_admin:209 - Admin user already exists, skipping creation
2025-08-01 10:20:36 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 10:20:36 | INFO     | app.core.startup:initialize_app:71 - Admin user already exists, skipping creation
2025-08-01 10:20:36 | INFO     | app.core.startup:initialize_app:81 - Application initialization completed
2025-08-01 10:20:36 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 10:20:36 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 10:30:31 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-08-01 10:30:31 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-08-01 10:30:31 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-08-01 10:30:31 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-08-01 10:30:31 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-08-01 10:30:31 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-08-01 10:30:37 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:30:37 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:30:37 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 10:30:37 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 10:30:37 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 10:30:37 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 10:30:37 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 10:30:37 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 10:30:37 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 10:30:37 | INFO     | app.core.startup:sync_api_scopes_on_startup:16 - Validating API scopes configuration...
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 1 (/auth/remove_roles_from_user) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 2 (/auth/assign_roles_to_user) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 3 (/auth/get_user_roles) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 4 (/auth/create_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 5 (/auth/delete_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 6 (/auth/create_permission) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 7 (/auth/assign_permission_to_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 8 (/auth/remove_permission_from_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 10 (/auth/revoke_token) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 11 (/auth/login) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 12 (/auth/register) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 13 (/auth/get_current_user_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 14 (/admin/api_permissions/sync_api_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 15 (/admin/api_permissions/validate_api_permissions_yml) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 16 (/admin/api_permissions/export_db_to_yaml) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 17 (/admin/api_permissions/get_all_api_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 18 (/admin/api_permissions/toggle_api_permission) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:30:37 | INFO     | app.core.sync_api_scopes:validate_config:335 - Configuration validation passed: 18 API scopes configurations
2025-08-01 10:30:37 | INFO     | app.core.startup:sync_api_scopes_on_startup:25 - API scopes configuration validation passed
2025-08-01 10:30:37 | INFO     | app.core.startup:sync_api_scopes_on_startup:28 - Syncing API scopes to database...
2025-08-01 10:30:37 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:183 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18
2025-08-01 10:30:37 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:214 - Checking 18 existing scopes for updates...
2025-08-01 10:30:37 | SUCCESS  | app.core.sync_api_scopes:sync_yml_to_db:240 - API scopes sync completed - Created: 0, Updated: 0, Deactivated: 0, Reactivated: 0, Skipped: 18
2025-08-01 10:30:37 | INFO     | app.core.startup:sync_api_scopes_on_startup:30 - API scopes sync completed: created 0, updated 0, deactivated 0
2025-08-01 10:30:37 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 10:30:37 | INFO     | app.core.init_data:init_admin:209 - Admin user already exists, skipping creation
2025-08-01 10:30:37 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 10:30:37 | INFO     | app.core.startup:initialize_app:71 - Admin user already exists, skipping creation
2025-08-01 10:30:37 | INFO     | app.core.startup:initialize_app:81 - Application initialization completed
2025-08-01 10:30:37 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 10:30:37 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 10:31:35 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-08-01 10:31:35 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-08-01 10:31:35 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-08-01 10:31:35 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-08-01 10:31:35 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-08-01 10:31:35 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-08-01 10:31:39 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:31:43 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:31:43 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:31:43 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 10:31:43 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 10:31:43 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 10:31:43 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 10:31:43 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 10:31:43 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 10:31:43 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 10:31:43 | INFO     | app.core.startup:sync_api_scopes_on_startup:16 - Validating API scopes configuration...
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 1 (/auth/remove_roles_from_user) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 2 (/auth/assign_roles_to_user) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 3 (/auth/get_user_roles) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 4 (/auth/create_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 5 (/auth/delete_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 6 (/auth/create_permission) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 7 (/auth/assign_permission_to_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 8 (/auth/remove_permission_from_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 10 (/auth/revoke_token) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 11 (/auth/login) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 12 (/auth/register) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 13 (/auth/get_current_user_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 14 (/admin/api_permissions/sync_api_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 15 (/admin/api_permissions/validate_api_permissions_yml) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 16 (/admin/api_permissions/export_db_to_yaml) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 17 (/admin/api_permissions/get_all_api_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | WARNING  | app.core.sync_api_scopes:validate_config:331 - Scopes configuration item 18 (/admin/api_permissions/toggle_api_permission) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:31:43 | INFO     | app.core.sync_api_scopes:validate_config:335 - Configuration validation passed: 18 API scopes configurations
2025-08-01 10:31:43 | INFO     | app.core.startup:sync_api_scopes_on_startup:25 - API scopes configuration validation passed
2025-08-01 10:31:43 | INFO     | app.core.startup:sync_api_scopes_on_startup:28 - Syncing API scopes to database...
2025-08-01 10:31:43 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:183 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18
2025-08-01 10:31:43 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:214 - Checking 18 existing scopes for updates...
2025-08-01 10:31:43 | SUCCESS  | app.core.sync_api_scopes:sync_yml_to_db:240 - API scopes sync completed - Created: 0, Updated: 0, Deactivated: 0, Reactivated: 0, Skipped: 18
2025-08-01 10:31:43 | INFO     | app.core.startup:sync_api_scopes_on_startup:30 - API scopes sync completed: created 0, updated 0, deactivated 0
2025-08-01 10:31:43 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 10:31:43 | INFO     | app.core.init_data:init_admin:209 - Admin user already exists, skipping creation
2025-08-01 10:31:43 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 10:31:43 | INFO     | app.core.startup:initialize_app:71 - Admin user already exists, skipping creation
2025-08-01 10:31:43 | INFO     | app.core.startup:initialize_app:81 - Application initialization completed
2025-08-01 10:31:43 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 10:31:43 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 10:41:37 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-08-01 10:41:37 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-08-01 10:41:37 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-08-01 10:41:37 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-08-01 10:41:37 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-08-01 10:41:37 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-08-01 10:41:41 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:41:41 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:41:41 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 10:41:41 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 10:41:41 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 10:41:41 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 10:41:41 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 10:41:41 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 10:41:41 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 10:41:41 | INFO     | app.core.startup:sync_api_scopes_on_startup:16 - Validating API scopes configuration...
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 1 (/auth/remove_roles_from_user) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 2 (/auth/assign_roles_to_user) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 3 (/auth/get_user_roles) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 4 (/auth/create_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 5 (/auth/delete_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 6 (/auth/create_permission) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 7 (/auth/assign_permission_to_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 8 (/auth/remove_permission_from_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 10 (/auth/revoke_token) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 11 (/auth/login) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 12 (/auth/register) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 13 (/auth/get_current_user_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 14 (/admin/api_permissions/sync_api_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 15 (/admin/api_permissions/validate_api_permissions_yml) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 16 (/admin/api_permissions/export_db_to_yaml) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 17 (/admin/api_permissions/get_all_api_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | WARNING  | app.core.sync_api_scopes:validate_config:333 - Scopes configuration item 18 (/admin/api_permissions/toggle_api_permission) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:41:41 | INFO     | app.core.sync_api_scopes:validate_config:337 - Configuration validation passed: 18 API scopes configurations
2025-08-01 10:41:41 | INFO     | app.core.startup:sync_api_scopes_on_startup:25 - API scopes configuration validation passed
2025-08-01 10:41:41 | INFO     | app.core.startup:sync_api_scopes_on_startup:28 - Syncing API scopes to database...
2025-08-01 10:41:41 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:184 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18, delete=0
2025-08-01 10:41:41 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:216 - Checking 18 existing scopes for updates...
2025-08-01 10:41:41 | SUCCESS  | app.core.startup:sync_api_scopes_on_startup:30 - API scopes sync completed: Created 0, Updated 0, Deactivated 0,Reactivated 0, Skipped 18
2025-08-01 10:41:41 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 10:41:41 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 10:41:41 | INFO     | app.core.startup:initialize_app:71 - Admin user already exists, skipping creation
2025-08-01 10:41:41 | INFO     | app.core.startup:initialize_app:81 - Application initialization completed
2025-08-01 10:41:41 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 10:41:41 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 10:46:39 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-08-01 10:46:39 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-08-01 10:46:39 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-08-01 10:46:39 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-08-01 10:46:39 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-08-01 10:46:39 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-08-01 10:47:04 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:47:04 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:47:04 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 10:47:04 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 10:47:04 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 10:47:04 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 10:47:04 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 10:47:04 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 10:47:04 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 10:47:04 | INFO     | app.core.startup:sync_api_scopes_on_startup:16 - Validating API scopes configuration...
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 1 (/auth/remove_roles_from_user) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 2 (/auth/assign_roles_to_user) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 3 (/auth/get_user_roles) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 4 (/auth/create_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 5 (/auth/delete_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 6 (/auth/create_permission) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 7 (/auth/assign_permission_to_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 8 (/auth/remove_permission_from_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 10 (/auth/revoke_token) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 11 (/auth/login) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 12 (/auth/register) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 13 (/auth/get_current_user_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 14 (/admin/api_permissions/sync_api_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 15 (/admin/api_permissions/validate_api_permissions_yml) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 16 (/admin/api_permissions/export_db_to_yaml) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 17 (/admin/api_permissions/get_all_api_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | WARNING  | app.core.sync_api_scopes:validate_config:348 - Scopes configuration item 18 (/admin/api_permissions/toggle_api_permission) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:47:04 | INFO     | app.core.sync_api_scopes:validate_config:352 - Configuration validation passed: 18 API scopes configurations
2025-08-01 10:47:04 | INFO     | app.core.startup:sync_api_scopes_on_startup:25 - API scopes configuration validation passed
2025-08-01 10:47:04 | INFO     | app.core.startup:sync_api_scopes_on_startup:28 - Syncing API scopes to database...
2025-08-01 10:47:04 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:187 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18, delete=0
2025-08-01 10:47:04 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:231 - Checking 18 existing scopes for updates...
2025-08-01 10:47:04 | SUCCESS  | app.core.startup:sync_api_scopes_on_startup:30 - API scopes sync completed: Created 0, Updated 0, Deleted 0, Deactivated 0,Reactivated 0, Skipped 18
2025-08-01 10:47:04 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 10:47:04 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 10:47:04 | INFO     | app.core.startup:initialize_app:71 - Admin user already exists, skipping creation
2025-08-01 10:47:04 | INFO     | app.core.startup:initialize_app:81 - Application initialization completed
2025-08-01 10:47:04 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 10:47:04 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 10:48:28 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-08-01 10:48:28 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-08-01 10:48:28 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-08-01 10:48:28 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-08-01 10:48:28 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-08-01 10:48:28 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-08-01 10:48:32 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:48:32 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 10:48:32 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 10:48:32 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 10:48:32 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 10:48:32 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 10:48:32 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 10:48:32 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 10:48:32 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 10:48:32 | INFO     | app.core.startup:sync_api_scopes_on_startup:16 - Validating API scopes configuration...
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 1 (/auth/remove_roles_from_user) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 2 (/auth/assign_roles_to_user) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 3 (/auth/get_user_roles) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 4 (/auth/create_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 5 (/auth/delete_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 6 (/auth/create_permission) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 7 (/auth/assign_permission_to_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 8 (/auth/remove_permission_from_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 10 (/auth/revoke_token) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 11 (/auth/login) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 12 (/auth/register) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 13 (/auth/get_current_user_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 14 (/admin/api_permissions/sync_api_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 15 (/admin/api_permissions/validate_api_permissions_yml) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 16 (/admin/api_permissions/export_db_to_yaml) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 17 (/admin/api_permissions/get_all_api_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | WARNING  | app.core.sync_api_scopes:validate_config:350 - Scopes configuration item 18 (/admin/api_permissions/toggle_api_permission) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 10:48:32 | INFO     | app.core.sync_api_scopes:validate_config:354 - Configuration validation passed: 18 API scopes configurations
2025-08-01 10:48:32 | INFO     | app.core.startup:sync_api_scopes_on_startup:25 - API scopes configuration validation passed
2025-08-01 10:48:32 | INFO     | app.core.startup:sync_api_scopes_on_startup:28 - Syncing API scopes to database...
2025-08-01 10:48:32 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:189 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18, delete=0
2025-08-01 10:48:32 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:233 - Checking 18 existing scopes for updates...
2025-08-01 10:48:32 | SUCCESS  | app.core.startup:sync_api_scopes_on_startup:30 - API scopes sync completed: Created 0, Updated 0, Deleted 0, Deactivated 0,Reactivated 0, Skipped 18
2025-08-01 10:48:32 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 10:48:32 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 10:48:32 | INFO     | app.core.startup:initialize_app:71 - Admin user already exists, skipping creation
2025-08-01 10:48:32 | INFO     | app.core.startup:initialize_app:81 - Application initialization completed
2025-08-01 10:48:32 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 10:48:32 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 16:05:09 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 16:05:24 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 16:05:24 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 16:05:24 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 16:05:24 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 16:05:24 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 16:05:24 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 16:05:24 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 16:05:24 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 16:05:24 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 16:05:24 | INFO     | app.core.startup:sync_api_scopes_on_startup:16 - Validating API scopes configuration...
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 1 (/auth/remove_roles_from_user) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 2 (/auth/assign_roles_to_user) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 3 (/auth/get_user_roles) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 4 (/auth/create_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 5 (/auth/delete_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 6 (/auth/create_permission) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 7 (/auth/assign_permission_to_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 8 (/auth/remove_permission_from_role) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 10 (/auth/revoke_token) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 11 (/auth/login) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 12 (/auth/register) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 13 (/auth/get_current_user_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 14 (/admin/api_permissions/sync_api_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 15 (/admin/api_permissions/validate_api_permissions_yml) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 16 (/admin/api_permissions/export_db_to_yaml) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 17 (/admin/api_permissions/get_all_api_permissions) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | WARNING  | app.core.sync_api_scopes:validate_config:351 - Scopes configuration item 18 (/admin/api_permissions/toggle_api_permission) warnings: Description too short (less than 10 characters), consider adding more details
2025-08-01 16:05:24 | INFO     | app.core.sync_api_scopes:validate_config:355 - Configuration validation passed: 18 API scopes configurations
2025-08-01 16:05:24 | INFO     | app.core.startup:sync_api_scopes_on_startup:25 - API scopes configuration validation passed
2025-08-01 16:05:24 | INFO     | app.core.startup:sync_api_scopes_on_startup:28 - Syncing API scopes to database...
2025-08-01 16:05:24 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:190 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18, delete=0
2025-08-01 16:05:24 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:234 - Checking 18 existing scopes for updates...
2025-08-01 16:05:24 | SUCCESS  | app.core.startup:sync_api_scopes_on_startup:30 - API scopes sync completed: Created 0, Updated 0, Deleted 0, Deactivated 0,Reactivated 0, Skipped 18
2025-08-01 16:05:24 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 16:05:24 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 16:05:24 | INFO     | app.core.startup:initialize_app:71 - Admin user already exists, skipping creation
2025-08-01 16:05:24 | INFO     | app.core.startup:initialize_app:81 - Application initialization completed
2025-08-01 16:05:24 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 16:05:24 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 16:07:44 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-08-01 16:07:44 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-08-01 16:07:44 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-08-01 16:07:44 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-08-01 16:07:44 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-08-01 16:07:44 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-08-01 16:07:53 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 16:08:35 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 16:08:35 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 16:08:36 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 16:08:36 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 16:08:36 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 16:08:36 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 16:08:36 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 16:08:36 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 16:08:36 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 16:08:36 | INFO     | app.core.startup:sync_api_scopes_on_startup:16 - Validating API scopes configuration...
2025-08-01 16:08:36 | INFO     | app.core.sync_api_scopes:validate_config:352 - Configuration validation passed: 18 API scopes configurations
2025-08-01 16:08:36 | INFO     | app.core.startup:sync_api_scopes_on_startup:25 - API scopes configuration validation passed
2025-08-01 16:08:36 | INFO     | app.core.startup:sync_api_scopes_on_startup:28 - Syncing API scopes to database...
2025-08-01 16:08:36 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:190 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18, delete=0
2025-08-01 16:08:36 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:234 - Checking 18 existing scopes for updates...
2025-08-01 16:08:36 | SUCCESS  | app.core.startup:sync_api_scopes_on_startup:30 - API scopes sync completed: Created 0, Updated 0, Deleted 0, Deactivated 0,Reactivated 0, Skipped 18
2025-08-01 16:08:36 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 16:08:36 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 16:08:36 | INFO     | app.core.startup:initialize_app:71 - Admin user already exists, skipping creation
2025-08-01 16:08:36 | INFO     | app.core.startup:initialize_app:81 - Application initialization completed
2025-08-01 16:08:36 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 16:08:36 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 16:08:58 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 16:12:38 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 17:04:58 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 17:04:59 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 17:04:59 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 17:04:59 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 17:04:59 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 17:04:59 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 17:05:04 | ERROR    | app.db.init_db:init_db:35 - 创建数据库表时出错: Multiple exceptions: [Errno 10061] Connect call failed ('::1', 25432, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 25432)
2025-08-01 17:05:55 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 17:05:57 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 17:05:57 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 17:05:58 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 17:05:58 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 17:05:58 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 17:05:58 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 17:05:58 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 17:05:58 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 17:05:58 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 17:05:58 | INFO     | app.core.startup:sync_api_scopes_on_startup:16 - Validating API scopes configuration...
2025-08-01 17:05:58 | INFO     | app.core.sync_api_scopes:validate_config:352 - Configuration validation passed: 18 API scopes configurations
2025-08-01 17:05:58 | INFO     | app.core.startup:sync_api_scopes_on_startup:25 - API scopes configuration validation passed
2025-08-01 17:05:58 | INFO     | app.core.startup:sync_api_scopes_on_startup:28 - Syncing API scopes to database...
2025-08-01 17:05:58 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:190 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18, delete=0
2025-08-01 17:05:58 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:234 - Checking 18 existing scopes for updates...
2025-08-01 17:05:58 | SUCCESS  | app.core.startup:sync_api_scopes_on_startup:30 - API scopes sync completed: Created 0, Updated 0, Deleted 0, Deactivated 0,Reactivated 0, Skipped 18
2025-08-01 17:05:58 | INFO     | app.core.startup:initialize_app:76 - Base data initialization on startup is disabled
2025-08-01 17:05:58 | INFO     | app.core.startup:initialize_app:81 - Application initialization completed
2025-08-01 17:05:58 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 17:05:58 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 17:07:18 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-08-01 17:07:18 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-08-01 17:07:18 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-08-01 17:07:18 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-08-01 17:07:18 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-08-01 17:07:18 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功
2025-08-01 17:07:21 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 17:07:21 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 17:07:21 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 17:07:21 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 17:07:21 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 17:07:21 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 17:07:21 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 17:07:21 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 17:07:21 | INFO     | app.core.startup:initialize_app:45 - Starting application initialization...
2025-08-01 17:07:21 | INFO     | app.core.startup:sync_api_scopes_on_startup:16 - Validating API scopes configuration...
2025-08-01 17:07:21 | INFO     | app.core.sync_api_scopes:validate_config:352 - Configuration validation passed: 18 API scopes configurations
2025-08-01 17:07:21 | INFO     | app.core.startup:sync_api_scopes_on_startup:25 - API scopes configuration validation passed
2025-08-01 17:07:21 | INFO     | app.core.startup:sync_api_scopes_on_startup:28 - Syncing API scopes to database...
2025-08-01 17:07:21 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:190 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18, delete=0
2025-08-01 17:07:21 | INFO     | app.core.sync_api_scopes:sync_yml_to_db:234 - Checking 18 existing scopes for updates...
2025-08-01 17:07:21 | SUCCESS  | app.core.startup:sync_api_scopes_on_startup:30 - API scopes sync completed: Created 0, Updated 0, Deleted 0, Deactivated 0,Reactivated 0, Skipped 18
2025-08-01 17:07:21 | INFO     | app.core.startup:initialize_app:54 - Initializing base data (roles, permissions, admin user)...
2025-08-01 17:07:21 | SUCCESS  | app.core.startup:initialize_app:59 - Base data initialization completed successfully
2025-08-01 17:07:21 | INFO     | app.core.startup:initialize_app:71 - Admin user already exists, skipping creation
2025-08-01 17:07:21 | INFO     | app.core.startup:initialize_app:81 - Application initialization completed
2025-08-01 17:07:21 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 17:07:21 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 17:28:13 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 17:28:16 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 17:28:16 | INFO     | app.core.utils:setup_logging:78 - 日志系统初始化完成，日志文件: D:\Projects_Code\Projects\FastAPI_Auth\logs\app.log
2025-08-01 17:28:16 | INFO     | app.main:lifespan:26 - Starting application initialization...
2025-08-01 17:28:16 | INFO     | app.db.init_db:init_db:23 - 创建数据库目录: D:\Projects_Code\Projects\FastAPI_Auth\data
2025-08-01 17:28:16 | INFO     | app.db.init_db:init_db:24 - 使用数据库URL: postgresql+asyncpg://heygo:heygo01!@localhost:25432/heygo
2025-08-01 17:28:16 | INFO     | app.db.init_db:init_db:29 - 创建数据库表...
2025-08-01 17:28:16 | SUCCESS  | app.db.init_db:init_db:31 - 数据库表创建成功
2025-08-01 17:28:16 | INFO     | app.db.redis:init_redis:33 - Redis connection successful
2025-08-01 17:28:16 | INFO     | app.services.init_data_service:initialize_app:19 - Starting application initialization...
2025-08-01 17:28:16 | INFO     | app.services.sync_service:sync_api_scopes_on_startup:19 - Validating API scopes configuration...
2025-08-01 17:28:16 | INFO     | app.services.sync_service:validate_config:377 - Configuration validation passed: 18 API scopes configurations
2025-08-01 17:28:16 | INFO     | app.services.sync_service:sync_api_scopes_on_startup:28 - API scopes configuration validation passed
2025-08-01 17:28:16 | INFO     | app.services.sync_service:sync_api_scopes_on_startup:31 - Syncing API scopes to database...
2025-08-01 17:28:16 | INFO     | app.services.sync_service:sync_yml_to_db:215 - Sync analysis: create=0, deactivate=0, reactivate=0, potentially_update=18, delete=0
2025-08-01 17:28:16 | INFO     | app.services.sync_service:sync_yml_to_db:259 - Checking 18 existing scopes for updates...
2025-08-01 17:28:16 | SUCCESS  | app.services.sync_service:sync_api_scopes_on_startup:33 - API scopes sync completed: Created 0, Updated 0, Deleted 0, Deactivated 0,Reactivated 0, Skipped 18
2025-08-01 17:28:16 | INFO     | app.services.init_data_service:initialize_app:28 - Initializing base data (roles, permissions, admin user)...
2025-08-01 17:28:16 | SUCCESS  | app.services.init_data_service:initialize_app:33 - Base data initialization completed successfully
2025-08-01 17:28:16 | INFO     | app.services.init_data_service:initialize_app:45 - Admin user already exists, skipping creation
2025-08-01 17:28:16 | INFO     | app.services.init_data_service:initialize_app:55 - Application initialization completed
2025-08-01 17:28:16 | INFO     | app.main:lifespan:46 - Application initialization transaction committed
2025-08-01 17:28:16 | SUCCESS  | app.main:lifespan:52 - 抖音推送下载app_env started successfully
2025-08-01 17:28:20 | INFO     | app.main:lifespan:58 - 正在关闭数据库引擎...
2025-08-01 17:28:20 | INFO     | app.main:lifespan:60 - 数据库引擎已关闭
2025-08-01 17:28:20 | INFO     | app.db.redis:close_redis:60 - Redis connection closed
2025-08-01 17:28:20 | INFO     | app.main:lifespan:68 - 正在关闭抖音分析浏览器...
2025-08-01 17:28:20 | INFO     | app.main:lifespan:70 - 抖音分析浏览器已关闭
2025-08-01 17:28:20 | INFO     | app.main:lifespan:74 - 抖音推送下载app_env关闭成功

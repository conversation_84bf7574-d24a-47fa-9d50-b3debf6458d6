# API权限管理系统

## 概述

本系统采用 **YAML配置文件 + 数据库** 的混合方案来管理API权限：

- **开发维护**：通过YAML文件管理权限配置
- **运行时查询**：从数据库读取权限配置（支持缓存）
- **动态管理**：支持通过管理接口临时调整权限

## 配置文件结构

### YAML配置文件位置
```
app/config/api_permissions.yml
```

### 配置文件格式
```yaml
api_permissions:
  # 分类名称
  category_name:
    - path: "/api/path"              # API路径
      description: "API描述"          # 描述信息
      required_scopes: ["scope1", "scope2"]  # 所需权限列表
      is_active: true                # 是否激活
```

### 示例配置
```yaml
api_permissions:
  user_management:
    - path: "/auth/remove_roles_from_user"
      description: "从用户移除角色"
      required_scopes: ["manage_users", "delete_record"]
      is_active: true
      
  public_apis:
    - path: "/auth/login"
      description: "用户登录"
      required_scopes: []  # 空数组表示无需权限
      is_active: true
```

## 使用方法

### 1. 修改权限配置

编辑 `app/config/api_permissions.yml` 文件：

```yaml
api_permissions:
  user_management:
    - path: "/auth/new_api"
      description: "新的API接口"
      required_scopes: ["manage_users"]
      is_active: true
```

### 2. 验证配置文件

```bash
python -m app.scripts.sync_permissions validate
```

### 3. 同步到数据库

```bash
python -m app.scripts.sync_permissions sync
```

### 4. 在代码中使用

```python
from app.services.auth_service import check_api_permission


@router.post("/your_api")
async def your_api(
        data: YourModel,
        db: AsyncSessionDep,
        _: None = Depends(check_api_permission("/your_api"))
):
   # 业务逻辑
   pass
```

## 管理命令

### 同步权限配置
```bash
python -m app.scripts.sync_permissions sync
```
- 将YAML配置同步到数据库
- 创建新的权限配置
- 更新已存在的配置
- 停用不在YAML中的配置

### 验证配置文件
```bash
python -m app.scripts.sync_permissions validate
```
- 检查YAML文件格式
- 验证必需字段
- 检查重复的API路径

### 导出数据库配置
```bash
python -m app.scripts.sync_permissions export
```
- 将数据库中的权限配置导出为YAML文件
- 输出文件：`app/config/api_permissions_export.yml`

## 管理API接口

### 权限同步接口
```http
POST /admin/permissions/sync
```

### 配置验证接口
```http
GET /admin/permissions/validate
```

### 权限列表接口
```http
GET /admin/permissions/list
```

### 切换权限状态
```http
PUT /admin/permissions/toggle/{permission_id}
```

## 应用启动时自动同步

在应用启动时，系统会自动：

1. 验证YAML配置文件
2. 同步权限配置到数据库
3. 记录同步统计信息

如果配置文件有错误，会在日志中记录错误信息。

## 最佳实践

### 1. 权限配置管理
- 所有权限配置都应该在YAML文件中定义
- 使用有意义的分类名称组织API
- 为每个API添加清晰的描述

### 2. 部署流程
1. 修改YAML配置文件
2. 提交到版本控制系统
3. 部署时自动同步权限配置
4. 验证权限配置是否生效

### 3. 紧急权限调整
- 可以通过管理接口临时停用某个API的权限检查
- 紧急调整后，应该及时更新YAML配置文件

### 4. 权限设计原则
- 遵循最小权限原则
- 使用组合权限（如：`["manage_users", "delete_record"]`）
- 公开API使用空权限列表：`required_scopes: []`

## 缓存机制

系统支持Redis缓存来提升性能：

```python
# 在 auth_service.py 中启用缓存
async def get_api_permission_scopes(api_path: str, db: AsyncSessionDep) -> list[str]:
    # 从Redis获取缓存
    cached_scopes = await redis_client.get(f"api_perm:{api_path}")
    if cached_scopes:
        return json.loads(cached_scopes)
    
    # 查询数据库并缓存结果
    # ...
```

## 故障排除

### 常见问题

1. **配置文件格式错误**
   - 使用 `validate` 命令检查格式
   - 确保YAML缩进正确

2. **权限同步失败**
   - 检查数据库连接
   - 查看错误日志

3. **API权限不生效**
   - 确认已同步到数据库
   - 检查API路径是否正确
   - 验证权限配置是否激活

### 日志查看
```bash
# 查看权限相关日志
grep "权限" logs/app.log
```

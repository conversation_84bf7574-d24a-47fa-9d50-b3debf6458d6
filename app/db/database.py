# app/db/database.py

"""
数据库连接和会话管理模块

提供异步数据库引擎和会话工厂，用于管理 数据库连接。
包含异步 SQLAlchemy 引擎配置和会话创建功能。
"""

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from app.config.env import settings

# 异步引擎
async_engine = create_async_engine(
    settings.DATABASE_URL,
    echo=False,  # 禁用 SQL 日志
    pool_pre_ping=True,  # 连接检查机制
    future=True,
    # 添加异步相关配置
    pool_size=20,
    max_overflow=0,
    pool_recycle=3600,
    # 确保正确的异步上下文
    connect_args={
        "server_settings": {
            "application_name": "FastAPI_Auth_App",
        }
    }
)

AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    expire_on_commit=False,
    class_=AsyncSession,
    # 确保异步上下文正确
    autoflush=True,
    autocommit=False
)
